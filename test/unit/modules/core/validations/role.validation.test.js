import {
  ModuleRepository,
  PolicySettingRepository,
  RoleModuleRepository,
  RoleRepository,
} from '#src/modules/user/repository/index.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  validateAndFilterModulePolicies,
  validateHierarchy,
  validatePoliciesForHierarchy,
  validateRoleName,
} from '#src/modules/core/validations/role.validation.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { RoleError } from '#src/modules/user/errors/index.js';

vi.mock('#src/modules/user/repository/index.js', () => ({
  ModuleRepository: {
    findById: vi.fn(),
  },
  PolicySettingRepository: {
    findByParentId: vi.fn(),
  },
  RoleModuleRepository: {
    findAllByRoleIdWithPolicySettings: vi.fn(),
  },
  RoleRepository: {
    findByEntityIdAndName: vi.fn(),
  },
}));

vi.mock('#src/modules/user/errors/index.js', () => ({
  RoleError: {
    roleNameExists: vi.fn().mockImplementation(() => {
      throw new Error('Role name exists');
    }),
    unsupportedModulePolicies: vi.fn().mockImplementation(() => {
      throw new Error('Unsupported policies');
    }),
    exceedsParentPolicies: vi.fn().mockImplementation(() => {
      throw new Error('Exceeds parent policies');
    }),
    parentHasNoPoliciesAccess: vi.fn().mockImplementation(() => {
      throw new Error('Parent has no policies access');
    }),
    invalidHierarchy: vi.fn().mockImplementation(() => {
      throw new Error('Invalid hierarchy');
    }),
  },
}));

vi.mock('#src/modules/core/errors/index.js', () => ({
  CoreError: {
    dataNotFound: vi.fn().mockImplementation(() => {
      throw new Error('Data not found');
    }),
  },
}));

describe('Role Validation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('validateRoleName', () => {
    it('should not throw error when role name is unique', async () => {
      RoleRepository.findByEntityIdAndName.mockResolvedValue(null);

      await expect(validateRoleName('server', 'entity-1', 'Admin')).resolves.not.toThrow();
      expect(RoleRepository.findByEntityIdAndName).toHaveBeenCalledWith(
        'server',
        'entity-1',
        'Admin',
        null,
      );
    });

    it('should throw error when role name is not unique', async () => {
      RoleRepository.findByEntityIdAndName.mockResolvedValue({ id: 'role-1', name: 'Admin' });

      await expect(validateRoleName('server', 'entity-1', 'Admin')).rejects.toThrow();
      expect(RoleError.roleNameExists).toHaveBeenCalled();
    });

    it('should exclude the specified role ID when checking uniqueness', async () => {
      RoleRepository.findByEntityIdAndName.mockResolvedValue(null);

      await validateRoleName('server', 'entity-1', 'Admin', 'role-1');
      expect(RoleRepository.findByEntityIdAndName).toHaveBeenCalledWith(
        'server',
        'entity-1',
        'Admin',
        'role-1',
      );
    });
  });

  describe('validateAndFilterModulePolicies', () => {
    const mockServer = 'server';
    const mockModules = [
      { moduleId: 'module-1', policies: ['read', 'write'] },
      { moduleId: 'module-2', policies: ['read'] },
    ];
    const mockHierarchy = 'organisation';

    beforeEach(() => {
      ModuleRepository.findById.mockImplementation((server, moduleId) => {
        const modules = {
          'module-1': { id: 'module-1', hierarchy: 'organisation' },
          'module-2': { id: 'module-2', hierarchy: 'merchant' },
        };
        return Promise.resolve(modules[moduleId]);
      });

      PolicySettingRepository.findByParentId.mockImplementation((server, moduleId) => {
        const policies = {
          'module-1': { read: true, write: true },
          'module-2': { read: true },
        };
        return Promise.resolve(policies[moduleId]);
      });
    });

    it('should validate and filter module policies without parent role', async () => {
      const result = await validateAndFilterModulePolicies(mockServer, mockModules, mockHierarchy);

      expect(result).toEqual([
        { moduleId: 'module-1', policies: ['read', 'write'] },
        { moduleId: 'module-2', policies: ['read'] },
      ]);
      expect(ModuleRepository.findById).toHaveBeenCalledTimes(2);
      expect(PolicySettingRepository.findByParentId).toHaveBeenCalledTimes(2);
    });

    it('should handle string module IDs', async () => {
      const stringModules = ['module-1', 'module-2'];

      const result = await validateAndFilterModulePolicies(
        mockServer,
        stringModules,
        mockHierarchy,
      );

      expect(result).toEqual([
        { moduleId: 'module-1', policies: [] },
        { moduleId: 'module-2', policies: [] },
      ]);
    });

    it('should throw error for unsupported policies', async () => {
      const modulesWithUnsupportedPolicies = [
        { moduleId: 'module-1', policies: ['read', 'delete'] },
      ];

      await expect(
        validateAndFilterModulePolicies(mockServer, modulesWithUnsupportedPolicies, mockHierarchy),
      ).rejects.toThrow();
      expect(RoleError.unsupportedModulePolicies).toHaveBeenCalledWith({
        module: undefined,
        policies: ['delete'],
      });
    });

    it('should validate against parent role policies', async () => {
      const parentId = 'parent-role-1';
      const mockParentPolicies = [
        {
          moduleId: 'module-1',
          policySetting: {
            toJSON: () => ({ read: true, write: false }),
          },
        },
        {
          moduleId: 'module-2',
          policySetting: {
            toJSON: () => ({ read: true }),
          },
        },
      ];

      RoleModuleRepository.findAllByRoleIdWithPolicySettings.mockResolvedValue(mockParentPolicies);

      const modulesExceedingParent = [{ moduleId: 'module-1', policies: ['read', 'write'] }];

      await expect(
        validateAndFilterModulePolicies(
          mockServer,
          modulesExceedingParent,
          mockHierarchy,
          parentId,
        ),
      ).rejects.toThrow();
      expect(RoleError.exceedsParentPolicies).toHaveBeenCalledWith();
    });

    it('should throw error if module is not in parent role', async () => {
      const parentId = 'parent-role-1';
      const mockParentPolicies = [
        {
          moduleId: 'module-1',
          policySetting: {
            toJSON: () => ({ read: true, write: true }),
          },
        },
      ];

      RoleModuleRepository.findAllByRoleIdWithPolicySettings.mockResolvedValue(mockParentPolicies);

      const modulesNotInParent = [{ moduleId: 'module-2', policies: ['read'] }];

      await expect(
        validateAndFilterModulePolicies(mockServer, modulesNotInParent, mockHierarchy, parentId),
      ).rejects.toThrow();
      expect(RoleError.parentHasNoPoliciesAccess).toHaveBeenCalledWith({
        module: undefined,
      });
    });

    it('should throw error when module is not found', async () => {
      ModuleRepository.findById.mockImplementation((server, moduleId) => {
        const modules = {
          'module-1': { id: 'module-1', hierarchy: 'organisation' },
          'module-2': { id: 'module-2', hierarchy: 'merchant' },
        };
        return Promise.resolve(modules[moduleId] || null);
      });

      const modulesWithNonExistent = [{ moduleId: 'non-existent-module', policies: ['read'] }];

      await expect(
        validateAndFilterModulePolicies(mockServer, modulesWithNonExistent, mockHierarchy),
      ).rejects.toThrow('Module not found');

      expect(ModuleRepository.findById).toHaveBeenCalledWith(mockServer, 'non-existent-module');
      expect(CoreError.dataNotFound).toHaveBeenCalledWith({
        data: 'common.label.module',
        attribute: 'ID',
        value: 'non-existent-module',
      });
    });
  });

  describe('validatePoliciesForHierarchy', () => {
    it('should return policies when module hierarchy is allowed for hierarchy', () => {
      const policies = ['read', 'write'];
      const result = validatePoliciesForHierarchy(policies, 'organisation', 'root');
      expect(result).toEqual(policies);
    });

    it('should throw error when module hierarchy is not allowed for hierarchy', () => {
      const policies = ['read', 'write'];
      expect(() => {
        validatePoliciesForHierarchy(policies, 'root', 'merchant');
      }).toThrow();
      expect(RoleError.invalidHierarchy).toHaveBeenCalledWith();
    });

    it('should validate correctly for organisation hierarchy', () => {
      const policies = ['read'];
      expect(validatePoliciesForHierarchy(policies, 'organisation', 'organisation')).toEqual(
        policies,
      );
      expect(validatePoliciesForHierarchy(policies, 'merchant', 'organisation')).toEqual(policies);
      expect(() => validatePoliciesForHierarchy(policies, 'root', 'organisation')).toThrow();
    });

    it('should validate correctly for merchant hierarchy', () => {
      const policies = ['read'];
      expect(validatePoliciesForHierarchy(policies, 'merchant', 'merchant')).toEqual(policies);
      expect(() => validatePoliciesForHierarchy(policies, 'organisation', 'merchant')).toThrow();
      expect(() => validatePoliciesForHierarchy(policies, 'root', 'merchant')).toThrow();
    });
  });

  describe('validateHierarchy', () => {
    it('should return true when role is within user hierarchy', () => {
      expect(validateHierarchy('root', 'root.admin')).toBe(true);
      expect(validateHierarchy('root.admin', 'root.admin.user')).toBe(true);
      expect(validateHierarchy('root', 'root')).toBe(true);
    });

    it('should return false when role is not within user hierarchy', () => {
      expect(validateHierarchy('root.admin', 'root.manager')).toBe(false);
      expect(validateHierarchy('root.admin.user', 'root.admin')).toBe(false);
      expect(validateHierarchy('org.admin', 'root.admin')).toBe(false);
    });
  });
});
