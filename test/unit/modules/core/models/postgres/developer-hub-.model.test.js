/* eslint-disable sonarjs/no-hardcoded-ip */
/* eslint-disable sonarjs/no-nested-functions */
/* eslint-disable sonarjs/no-duplicate-string */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Model } from 'sequelize';

import { auditableMixin, versionedMixin } from '#src/mixins/index.js';
import { DeveloperHubError } from '#src/modules/setting/errors/index.js';
import DeveloperHubModel from '#src/modules/core/models/postgres/developer-hub.model.js';
import { DeveloperHubRepository } from '#src/modules/setting/repository/index.js';

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
  versionedMixin: {
    applyVersioning: vi.fn(),
  },
}));

vi.mock('#src/modules/setting/repository/index.js', () => ({
  DeveloperHubRepository: {
    findByApiKey: vi.fn(),
  },
}));

vi.mock('sequelize', async () => {
  const actual = await vi.importActual('sequelize');
  return {
    ...actual,
    DataTypes: {
      UUID: 'UUID',
      STRING: vi.fn(() => 'STRING'),
      DATE: vi.fn(() => 'DATE'),
      ENUM: vi.fn(() => 'ENUM'),
      BIGINT: 'BIGINT',
      VIRTUAL: 'VIRTUAL',
    },
    Model: class MockModel {
      static init() {}
      static hasMany() {}
    },
    Sequelize: {
      literal: vi.fn((val) => val),
    },
  };
});

describe('DeveloperHub Model', () => {
  let mockFastify;
  let DeveloperHub;

  beforeEach(() => {
    vi.resetAllMocks();
    mockFastify = {
      psql: {
        connection: {},
      },
    };
    DeveloperHub = DeveloperHubModel(mockFastify);
  });

  it('should define correct associations', () => {
    const models = {
      ApiRight: {},
      IpAccessControl: {},
      Remark: {},
    };
    const hasManySpy = vi.spyOn(Model, 'hasMany');
    DeveloperHub.associate(models);

    expect(hasManySpy).toHaveBeenCalledWith(models.ApiRight, {
      foreignKey: 'parentId',
      as: 'apiRights',
    });

    expect(hasManySpy).toHaveBeenCalledWith(models.IpAccessControl, {
      foreignKey: 'parentId',
      as: 'ipAccessControls',
      scope: {
        status: 'active',
      },
      include: [
        {
          model: models.Remark,
          as: 'activeRemark',
          scope: {
            remarkable_type: 'ip_access_control',
            status: 'active',
          },
        },
      ],
    });
  });

  it('should apply audit and versioning mixins', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(DeveloperHub);
    expect(versionedMixin.applyVersioning).toHaveBeenCalledWith(DeveloperHub);
  });

  describe('Hooks', () => {
    let hooks;

    beforeEach(() => {
      const initSpy = vi.spyOn(Model, 'init');
      DeveloperHubModel(mockFastify);
      hooks = initSpy.mock.calls[0][1].hooks;
    });

    describe('beforeCreate', () => {
      it('should hash the apiKey and check for duplicates', async () => {
        const instance = {
          constructor: DeveloperHub,
          apiKey: 'plain-key',
        };
        DeveloperHubRepository.findByApiKey.mockResolvedValue(null);

        await hooks.beforeCreate(instance);

        expect(instance.apiKey).toMatch(/^[a-f0-9]{64}$/); // SHA256 hex
        expect(DeveloperHubRepository.findByApiKey).toHaveBeenCalledWith(
          { psql: { DeveloperHub } },
          instance.apiKey,
        );
      });

      it('should throw if apiKey already exists', async () => {
        const instance = {
          constructor: DeveloperHub,
          apiKey: 'duplicate-key',
        };
        DeveloperHubRepository.findByApiKey.mockResolvedValue({ id: 'some-id' });

        await expect(hooks.beforeCreate(instance)).rejects.toThrow(
          DeveloperHubError.apiKeyExists({
            attribute: 'common.label.apiKey',
            value: 'duplicate-key',
          }),
        );
      });
    });
  });

  describe('Virtual field', () => {
    it('should return ip addresses from ipAccessControls', () => {
      const instance = {
        ipAccessControls: [{ ipAddress: '*******' }, { ipAddress: '*******' }],
      };
      const ipGetter =
        DeveloperHub.rawAttributes?.ipAddresses?.get ||
        DeveloperHub.init.mock?.calls?.[0]?.[0]?.ipAddresses?.get;

      expect(ipGetter.call(instance)).toEqual(['*******', '*******']);
    });

    it('should return null if ipAccessControls is not present', () => {
      const instance = {};
      const ipGetter =
        DeveloperHub.rawAttributes?.ipAddresses?.get ||
        DeveloperHub.init.mock?.calls?.[0]?.[0]?.ipAddresses?.get;

      expect(ipGetter.call(instance)).toBeNull();
    });
  });
});
