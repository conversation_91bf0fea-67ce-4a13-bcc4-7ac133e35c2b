import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Model } from 'sequelize';

import ApiRightModel from '#src/modules/core/models/postgres/api-right.model.js';
import { auditableMixin } from '#src/mixins/index.js';

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    PERMISSION_FIELDS: ['canRead', 'canCreate', 'canEdit', 'canManage', 'canImport', 'canExport'],
  },
}));

vi.mock('#src/modules/setting/errors/index.js', () => ({
  DeveloperHubError: {
    atLeastOneRequired: vi
      .fn()
      .mockReturnValue(new Error('At least one permission must be provided.')),
  },
}));

describe('ApiRight Model', () => {
  let mockFastify;
  let ApiRight;

  beforeEach(() => {
    vi.resetAllMocks();
    Model.init = vi.fn();
    Model.belongsTo = vi.fn();
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    ApiRight = ApiRightModel(mockFastify);
  });

  it('should define correct associations', () => {
    const mockModels = {
      DeveloperHub: {},
    };
    ApiRight.associate(mockModels);
    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.DeveloperHub, {
      foreignKey: 'parentId',
      as: 'developerHub',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  });

  it('should call auditableMixin.applyAuditFields', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(ApiRight);
  });

  describe('Model initialization', () => {
    let initOptions;

    beforeEach(() => {
      initOptions = Model.init.mock.calls[0][1];
    });

    it('should set correct model name and table name', () => {
      expect(initOptions.modelName).toBe('ApiRight');
      expect(initOptions.tableName).toBe('api_rights');
    });

    it('should set underscored and timestamps to true', () => {
      expect(initOptions.underscored).toBe(true);
      expect(initOptions.timestamps).toBe(true);
    });

    it('should set the correct sequelize instance', () => {
      expect(initOptions.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct index', () => {
      expect(initOptions.indexes).toEqual([
        {
          unique: true,
          fields: ['parent_id', 'module_id'],
        },
      ]);
    });

    it('should define beforeCreate and beforeUpdate hooks', () => {
      expect(typeof initOptions.hooks.beforeCreate).toBe('function');
      expect(typeof initOptions.hooks.beforeUpdate).toBe('function');
    });
  });

  describe('Hooks', () => {
    let initOptions;

    beforeEach(() => {
      initOptions = Model.init.mock.calls[0][1];
    });

    it('should throw an error if no permissions are granted in beforeCreate', async () => {
      const instance = {
        canRead: false,
        canCreate: false,
        canEdit: false,
        canManage: false,
        canImport: false,
        canExport: false,
      };
      await expect(initOptions.hooks.beforeCreate(instance)).rejects.toThrow(
        'At least one permission must be provided.',
      );
    });

    it('should not throw an error if at least one permission is granted in beforeCreate', async () => {
      const instance = {
        canRead: true,
        canCreate: false,
        canEdit: false,
        canManage: false,
        canImport: false,
        canExport: false,
      };
      await expect(initOptions.hooks.beforeCreate(instance)).resolves.not.toThrow();
    });

    it('should throw an error if no permissions are granted in beforeUpdate', async () => {
      const instance = {
        canRead: false,
        canCreate: false,
        canEdit: false,
        canManage: false,
        canImport: false,
        canExport: false,
      };
      await expect(initOptions.hooks.beforeUpdate(instance)).rejects.toThrow(
        'At least one permission must be provided.',
      );
    });

    it('should not throw an error if at least one permission is granted in beforeUpdate', async () => {
      const instance = {
        canRead: false,
        canCreate: true,
        canEdit: false,
        canManage: false,
        canImport: false,
        canExport: false,
      };
      await expect(initOptions.hooks.beforeUpdate(instance)).resolves.not.toThrow();
    });
  });

  describe('Virtual fields', () => {
    let initOptions;

    beforeEach(() => {
      initOptions = Model.init.mock.calls[0][0];
    });

    describe('moduleName', () => {
      it('should return correct moduleName for known moduleId', () => {
        const getter = initOptions.moduleName.get;
        const context = { moduleId: 'a7f95e10-0f85-11f0-af17-f33274f90ad1' };
        expect(getter.call(context)).toBe('Member');

        context.moduleId = 'a7f95e10-0f85-11f0-af17-f33274f90ad2';
        expect(getter.call(context)).toBe('Transaction');
      });

      it('should return "Unknown" for unknown moduleId', () => {
        const getter = initOptions.moduleName.get;
        const context = { moduleId: 'unknown-id' };
        expect(getter.call(context)).toBe('Unknown');
      });
    });

    describe('permissions', () => {
      it('should return correct permissions array', () => {
        const getter = initOptions.permissions.get;
        const context = {
          canRead: true,
          canCreate: false,
          canEdit: true,
          canManage: false,
          canImport: true,
          canExport: false,
        };

        expect(getter.call(context)).toEqual(['canRead', 'canEdit', 'canImport']);
      });

      it('should return an empty array when no permissions are set', () => {
        const getter = initOptions.permissions.get;
        const context = {
          canRead: false,
          canCreate: false,
          canEdit: false,
          canManage: false,
          canImport: false,
          canExport: false,
        };

        expect(getter.call(context)).toEqual([]);
      });
    });
  });
});
