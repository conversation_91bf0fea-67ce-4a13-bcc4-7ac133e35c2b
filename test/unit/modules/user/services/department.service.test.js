import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as departmentService from '#src/modules/user/services/department.service.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { DepartmentConstant } from '#src/modules/user/constants/index.js';
import { DepartmentRepository } from '#src/modules/user/repository/index.js';
import { ModuleRepository } from '#src/modules/core/repository/index.js';
import { fetchFromCache } from '#src/utils/cache.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

const { HIERARCHY } = DepartmentConstant;

vi.mock('#src/modules/user/repository/index.js');
vi.mock('#src/modules/core/repository/index.js');
vi.mock('#src/utils/db-transaction.util.js', () => ({
  withTransaction: vi.fn((_, __, callback) => callback('tx')),
}));
vi.mock('#src/utils/cache.util.js', () => ({
  fetchFromCache: vi.fn((redis, key, fallback) => fallback()),
}));

const fakeServer = { redis: {} };
const fakeEntity = { id: 'entity-1', hierarchy: 'root' };
const fakeAuthInfo = { id: 'auth-1' };
const fakeDepartment = {
  id: 'dep-1',
  toJSON: () => ({ id: 'dep-1', departmentModules: [] }),
};
const fakeModule = {
  id: 'mod-1',
  name: 'Module',
  hierarchy: 'merchant',
  parentId: null,
  translationKey: 'translation.key',
  level: 1,
  policySetting: { toJSON: () => ({ canView: true, canEdit: false }) },
};

describe('departmentService', () => {
  let mockRequest;

  beforeEach(() => {
    mockRequest = {
      server: fakeServer,
      params: { id: 'dep-1' },
      authInfo: { id: 'auth-123' },
      body: [
        {
          module_id: 'mod-1',
          policySetting: ['canView', 'canEdit'],
        },
      ],
    };

    vi.restoreAllMocks();
  });

  it('index returns departments for entity', async () => {
    DepartmentRepository.findAll.mockResolvedValue(['dep1']);
    const result = await departmentService.index(
      {
        entity: fakeEntity,
        query: {},
        server: fakeServer,
      },
      true,
    );
    expect(result).toEqual(['dep1']);
  });

  it('view returns department and formats modules', async () => {
    const department = {
      ...fakeDepartment,
      departmentModules: [
        {
          module: fakeModule,
          policySetting: { toJSON: () => ({ canView: true, canEdit: false }) },
        },
      ],
    };
    DepartmentRepository.findById.mockResolvedValue(department);
    const result = await departmentService.view({
      params: { id: 'dep-1' },
      server: fakeServer,
    });
    expect(result.modules).toHaveProperty('merchant');
  });

  it('view throws if not found', async () => {
    DepartmentRepository.findById.mockResolvedValue(null);
    await expect(
      departmentService.view({
        params: { id: 'missing' },
        server: fakeServer,
      }),
    ).rejects.toThrow(
      CoreError.dataNotFound({
        data: 'common.label.department',
        attribute: 'ID',
        value: 'missing',
      }),
    );
  });

  it('create creates a department and attaches modules', async () => {
    const fakeModule = {
      id: 'mod-1',
      hierarchy: 'merchant',
      policySetting: { toJSON: () => ({ canView: true }) },
    };

    const fakeDepartment = {
      id: 'dep-1',
      toJSON: () => ({ id: 'dep-1' }),
      departmentModules: [
        {
          module: {
            id: 'mod-1',
            name: 'Module 1',
            hierarchy: 'merchant',
            translationKey: 'mod1.key',
            level: 1,
            parentId: null,
          },
          policySetting: { toJSON: () => ({ canView: true }) },
        },
      ],
    };

    ModuleRepository.findAll.mockResolvedValue([fakeModule]);
    DepartmentRepository.createDepartment.mockResolvedValue(fakeDepartment);
    DepartmentRepository.createDepartmentModule.mockResolvedValue({ id: 'dm-1' });
    DepartmentRepository.upsertPolicySetting.mockResolvedValue();

    DepartmentRepository.findById.mockResolvedValue(fakeDepartment);

    withTransaction.mockImplementation(async (_s, _opt, callback) => {
      return await callback({});
    });

    const request = {
      body: {
        name: 'Finance',
        description: 'Handles finances',
        hierarchy: 'merchant',
        status: 'active',
        modules: [{ module_id: 'mod-1', policySetting: ['canView'] }],
      },
      entity: { id: 'ent-1' },
      authInfo: { id: 'admin-user' },
      server: fakeServer,
    };

    const result = await departmentService.create(request, false);

    expect(DepartmentRepository.createDepartment).toHaveBeenCalled();
    expect(DepartmentRepository.createDepartmentModule).toHaveBeenCalled();
    expect(DepartmentRepository.upsertPolicySetting).toHaveBeenCalled();
    expect(result.id).toBe('dep-1');
  });

  it('create skips module if module_id is not found', async () => {
    const fakeDepartment = { id: 'dep-1' };

    ModuleRepository.findAll.mockResolvedValue([]);
    DepartmentRepository.createDepartment.mockResolvedValue(fakeDepartment);
    DepartmentRepository.findById.mockResolvedValue({
      ...fakeDepartment,
      departmentModules: [],
      toJSON: () => fakeDepartment,
    });

    const request = {
      body: {
        name: 'Unknown Module Dept',
        description: 'Test department',
        hierarchy: 'merchant',
        status: 'active',
        modules: [
          {
            module_id: 'non-existent-module-id',
            policySetting: ['canView', 'canEdit'],
          },
        ],
      },
      server: fakeServer,
      entity: { id: 'ent-1' },
      authInfo: { id: 'user-1' },
    };

    const result = await departmentService.create(request, false);

    expect(ModuleRepository.findAll).toHaveBeenCalled();
    expect(DepartmentRepository.createDepartmentModule).not.toHaveBeenCalled();
    expect(DepartmentRepository.upsertPolicySetting).not.toHaveBeenCalled();
    expect(result.id).toBe(fakeDepartment.id);
  });

  describe('updateBasicInformation', () => {
    it('updates basic info when changes are made', async () => {
      const departmentId = 'dep-1';
      const updateData = { name: 'New Name' };
      const authInfoId = 'auth-1';

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.update.mockResolvedValue({ isDirty: true });

      await departmentService.updateBasicInformation({
        params: { id: departmentId },
        body: updateData,
        authInfo: { id: authInfoId },
        server: fakeServer,
      });

      expect(DepartmentRepository.findById).toHaveBeenCalledWith(fakeServer, departmentId);
      expect(DepartmentRepository.update).toHaveBeenCalledWith(fakeDepartment, updateData, {
        authInfoId,
      });
    });

    it('throws CoreError.dataNotFound if department is not found', async () => {
      const departmentId = 'non-existent-dep';

      DepartmentRepository.findById.mockResolvedValue(null);

      await expect(
        departmentService.updateBasicInformation({
          params: { id: departmentId },
          body: { name: 'New Name' },
          authInfo: { id: 'auth-1' },
          server: fakeServer,
        }),
      ).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.department',
          attribute: 'ID',
          value: departmentId,
        }),
      );
    });

    it('throws CoreError.unprocessable if no changes are made', async () => {
      const departmentId = 'dep-1';
      const updateData = { name: 'Existing Name' };

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.update.mockResolvedValue({ isDirty: false });

      await expect(
        departmentService.updateBasicInformation({
          params: { id: departmentId },
          body: updateData,
          authInfo: { id: 'auth-1' },
          server: fakeServer,
        }),
      ).rejects.toThrow(CoreError.unprocessable());
    });
  });

  describe('updateStatus', () => {
    it('updates department status when changes are made', async () => {
      const departmentId = 'dep-1';
      const newStatus = 'inactive';
      const authInfoId = 'auth-1';

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.update.mockResolvedValue({ isDirty: true });

      await departmentService.updateStatus({
        params: { id: departmentId },
        body: { status: newStatus },
        authInfo: { id: authInfoId },
        server: fakeServer,
      });

      expect(DepartmentRepository.findById).toHaveBeenCalledWith(fakeServer, departmentId);
      expect(DepartmentRepository.update).toHaveBeenCalledWith(
        fakeDepartment,
        { status: newStatus },
        { authInfoId },
      );
    });

    it('throws CoreError.dataNotFound if department is not found', async () => {
      const departmentId = 'non-existent-dep';

      DepartmentRepository.findById.mockResolvedValue(null);

      await expect(
        departmentService.updateStatus({
          params: { id: departmentId },
          body: { status: 'inactive' },
          authInfo: { id: 'auth-1' },
          server: fakeServer,
        }),
      ).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.department',
          attribute: 'ID',
          value: departmentId,
        }),
      );
    });

    it('throws CoreError.unprocessable if no changes are made', async () => {
      const departmentId = 'dep-1';
      const currentStatus = 'active';

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.update.mockResolvedValue({ isDirty: false });

      await expect(
        departmentService.updateStatus({
          params: { id: departmentId },
          body: { status: currentStatus },
          authInfo: { id: 'auth-1' },
          server: fakeServer,
        }),
      ).rejects.toThrow(CoreError.unprocessable());
    });
  });

  it('remove soft-deletes and removes department', async () => {
    DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
    DepartmentRepository.update.mockResolvedValue();
    DepartmentRepository.remove.mockResolvedValue('removed');

    withTransaction.mockImplementation(async (_, __, cb) => await cb('tx'));

    const result = await departmentService.remove({
      params: { id: 'dep-1' },
      authInfo: fakeAuthInfo,
      server: fakeServer,
    });

    expect(result).toBe('removed');
  });

  it('remove throws if department not found', async () => {
    const departmentId = 'dep-404';

    DepartmentRepository.findById.mockResolvedValue(null);

    const request = {
      params: { id: departmentId },
      authInfo: { id: 'auth-1' },
      server: fakeServer,
    };

    await expect(departmentService.remove(request)).rejects.toThrowError(
      CoreError.dataNotFound({
        data: 'common.label.department',
        attribute: 'ID',
        value: departmentId,
      }),
    );

    expect(DepartmentRepository.findById).toHaveBeenCalledWith(fakeServer, departmentId);
  });

  it('getModulePolicyOptions fetches from cache and filters', async () => {
    ModuleRepository.findModulePolicies.mockResolvedValue([fakeModule]);
    fetchFromCache.mockImplementation(async (_, __, cb) => await cb());
    const result = await departmentService.getModulePolicyOptions({
      server: fakeServer,
      entity: fakeEntity,
    });
    expect(result).toHaveProperty('merchant');
  });

  it('getModulePolicyOptions returns structured modules', async () => {
    const fakeModules = [
      /* fake modules */
    ];

    ModuleRepository.findModulePolicies = vi.fn().mockResolvedValue(fakeModules);
    fetchFromCache.mockResolvedValue({ ROOT: [], ORGANISATION: [], MERCHANT: [] });

    const result = await departmentService.getModulePolicyOptions({
      server: fakeServer,
      entity: { id: 'ent-1', hierarchy: 'merchant' },
    });

    expect(result).toBeDefined();
    expect(fetchFromCache).toHaveBeenCalled();
  });

  it('throws an error for invalid hierarchy', async () => {
    const invalidRequest = {
      server: { redis: {} },
      entity: {
        id: 'entity-xyz',
        hierarchy: 'INVALID_ACCESS_LEVEL',
      },
    };

    await expect(departmentService.getModulePolicyOptions(invalidRequest)).rejects.toThrow(
      'Invalid hierarchy',
    );
  });

  it('returns structured modules for ORGANISATION hierarchy', async () => {
    const mockModules = [
      {
        id: 'mod-1',
        name: 'Organisation Module',
        hierarchy: HIERARCHY.ORGANISATION,
        navigationPosition: 1,
        navigationType: 'menu',
        translationKey: 'module.org',
        level: 1,
        parentId: null,
        policySetting: {
          toJSON: () => ({
            canView: true,
            canEdit: false,
          }),
        },
        children: [],
      },
    ];

    const mockServer = { redis: {} };
    const mockRequest = {
      server: mockServer,
      entity: {
        id: 'entity-001',
        hierarchy: HIERARCHY.ORGANISATION,
      },
    };

    ModuleRepository.findModulePolicies.mockResolvedValue(mockModules);

    const result = await departmentService.getModulePolicyOptions(mockRequest);

    expect(ModuleRepository.findModulePolicies).toHaveBeenCalledWith(mockServer, [
      HIERARCHY.ORGANISATION,
      HIERARCHY.MERCHANT,
    ]);

    expect(result[HIERARCHY.ORGANISATION]).toEqual([
      expect.objectContaining({
        id: 'mod-1',
        name: 'Organisation Module',
      }),
    ]);
  });

  describe('departmentService.updateModulePolicy', () => {
    const fakeServer = { db: {} };
    const authInfoId = 'auth-123';
    const departmentId = 'dep-1';
    const fakeDepartment = { id: departmentId, hierarchy: 'merchant' };
    const fakeModules = [
      {
        id: 'mod-1',
        hierarchy: 'merchant',
        policySetting: {
          toJSON: () => ({
            canView: true,
            canEdit: true,
            canCreate: false,
          }),
        },
      },
    ];
    const fakeDeptModule = {
      moduleId: 'mod-1',
      id: 'deptmod-1',
    };

    const request = {
      server: fakeServer,
      params: { id: 'dep-1' },
      authInfo: { id: authInfoId },
      body: [
        {
          module_id: 'mod-1',
          policySetting: ['canView', 'canEdit'],
        },
      ],
    };

    beforeEach(() => {
      vi.clearAllMocks();

      DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
      DepartmentRepository.findAllDepartmentModules.mockResolvedValue([fakeDeptModule]);
      DepartmentRepository.removeDepartmentModule.mockResolvedValue(true);

      ModuleRepository.findAll.mockResolvedValue(fakeModules);

      withTransaction.mockImplementation(async (server, options, cb) => await cb({}));
    });

    it('skips upsert if module not found', async () => {
      ModuleRepository.findAll.mockResolvedValue([]);

      const spy = vi.spyOn(departmentService, 'upsertDepartmentModule');

      await departmentService.updateModulePolicy(request);

      expect(spy).not.toHaveBeenCalled();
    });

    it('removes department module when no valid policies', async () => {
      request.body[0].policySetting = [];

      const removeSpy = vi.spyOn(DepartmentRepository, 'removeDepartmentModule');

      await departmentService.updateModulePolicy(request);

      expect(removeSpy).toHaveBeenCalledWith(
        fakeServer,
        fakeDeptModule.id,
        expect.objectContaining({
          transaction: expect.anything(),
          authInfoId,
        }),
      );
    });

    it('throws CoreError.dataNotFound if department not found', async () => {
      DepartmentRepository.findById.mockResolvedValue(null);

      await expect(departmentService.updateModulePolicy(request)).rejects.toThrow(
        CoreError.dataNotFound({
          data: 'common.label.department',
          attribute: 'ID',
          value: departmentId,
        }),
      );
    });

    it('throws CoreError.unprocessable if no changes made', async () => {
      const emptyReq = {
        ...request,
        body: [],
      };

      DepartmentRepository.findAllDepartmentModules.mockResolvedValue([]);

      await expect(departmentService.updateModulePolicy(emptyReq)).rejects.toThrow(
        CoreError.unprocessable(),
      );
    });
  });

  it('upserts department module when valid policies exist', async () => {
    const departmentId = 'dep-1';
    const moduleId = 'mod-1';

    const fakeDepartment = { id: departmentId, hierarchy: 'merchant' };
    const fakeModule = {
      id: moduleId,
      hierarchy: 'merchant',
      policySetting: {
        toJSON: () => ({
          canView: true,
          canEdit: true,
          canCreate: false,
        }),
      },
    };

    const fakeDepartmentModule = { id: 'dm-1' };

    DepartmentRepository.findById.mockResolvedValue(fakeDepartment);
    ModuleRepository.findAll.mockResolvedValue([fakeModule]);
    DepartmentRepository.findAllDepartmentModules.mockResolvedValue([]);
    DepartmentRepository.findDepartmentModule.mockResolvedValue(null);
    DepartmentRepository.createDepartmentModule.mockResolvedValue(fakeDepartmentModule);
    DepartmentRepository.upsertPolicySetting.mockResolvedValue({ isDirty: true });

    await departmentService.updateModulePolicy(mockRequest);

    expect(DepartmentRepository.findDepartmentModule).toHaveBeenCalledWith(
      fakeServer,
      departmentId,
      moduleId,
      expect.objectContaining({ transaction: expect.anything() }),
    );

    expect(DepartmentRepository.createDepartmentModule).toHaveBeenCalledWith(
      fakeServer,
      { departmentId, moduleId },
      expect.objectContaining({ transaction: expect.anything(), authInfoId: 'auth-123' }),
    );

    expect(DepartmentRepository.upsertPolicySetting).toHaveBeenCalledWith(
      fakeServer,
      expect.objectContaining({
        parentId: fakeDepartmentModule.id,
        canView: true,
        canEdit: true,
        canCreate: false,
        canImport: false,
        canExport: false,
        canManage: false,
        canMasking: false,
        canOverwrite: false,
        canVerify: false,
      }),
      expect.objectContaining({
        transaction: expect.anything(),
        authInfoId: 'auth-123',
      }),
    );

    expect(DepartmentRepository.removeDepartmentModule).not.toHaveBeenCalled();
  });

  it('validateAndFilterPolicies returns valid policies or null', () => {
    const module = {
      hierarchy: 'merchant',
      policySetting: { toJSON: () => ({ canView: true }) },
    };
    expect(
      departmentService.validateAndFilterPolicies(module, 'merchant', ['canView', 'canEdit']),
    ).toEqual(['canView']);
    expect(departmentService.validateAndFilterPolicies(module, 'root', ['canView'])).toEqual([
      'canView',
    ]);
    expect(
      departmentService.validateAndFilterPolicies(module, 'organisation', ['canView']),
    ).toEqual(['canView']);
    expect(departmentService.validateAndFilterPolicies(module, 'merchant', ['canEdit'])).toEqual(
      [],
    );
    module.hierarchy = 'root';
    expect(departmentService.validateAndFilterPolicies(module, 'merchant', ['canView'])).toBe(null);
  });

  it('formatModule returns cleaned policy structure', () => {
    const formatted = departmentService.formatModule(fakeModule);
    expect(formatted).toHaveProperty('policySetting');
    expect(formatted).toHaveProperty('id', 'mod-1');
  });

  it('includes parentId when it is not null', () => {
    const inputModule = {
      id: 'mod-1',
      name: 'Test Module',
      hierarchy: 'merchant',
      navigationPosition: 1,
      navigationType: 'menu',
      translationKey: 'test.module',
      level: 1,
      parentId: 'parent-1',
      policySetting: {
        toJSON: () => ({
          canView: true,
          canCreate: false,
        }),
      },
    };

    const result = departmentService.formatModule(inputModule);

    expect(result).toMatchObject({
      id: 'mod-1',
      parentId: 'parent-1',
    });
  });

  it('filterAndStructureModules organizes modules by hierarchy', () => {
    const result = departmentService.filterAndStructureModules([fakeModule], ['merchant']);
    expect(result.merchant.length).toBeGreaterThan(0);
  });

  it('should filter out modules with disallowed hierarchies', () => {
    const modules = [
      { id: '1', name: 'Root Module', hierarchy: HIERARCHY.ROOT, parentId: null },
      { id: '2', name: 'Org Module', hierarchy: HIERARCHY.ORGANISATION, parentId: null },
      { id: '3', name: 'Merchant Module', hierarchy: HIERARCHY.MERCHANT, parentId: null },
    ];

    const allowedHierarchies = [HIERARCHY.ORGANISATION, HIERARCHY.MERCHANT];

    const result = departmentService.filterAndStructureModules(modules, allowedHierarchies);

    expect(result[HIERARCHY.ROOT]).toHaveLength(0);
    expect(result[HIERARCHY.ORGANISATION]).toHaveLength(1);
    expect(result[HIERARCHY.MERCHANT]).toHaveLength(1);
    expect(result[HIERARCHY.ORGANISATION][0].name).toBe('Org Module');
    expect(result[HIERARCHY.MERCHANT][0].name).toBe('Merchant Module');
  });

  it('structures modules with children recursively', () => {
    const modules = [
      {
        id: 'mod-root-1',
        name: 'Root Module',
        hierarchy: HIERARCHY.ROOT,
        navigationPosition: 1,
        navigationType: 'menu',
        translationKey: 'module.root',
        level: 1,
        parentId: null,
        policySetting: {
          toJSON: () => ({
            canView: true,
            canEdit: true,
          }),
        },
        children: [
          {
            id: 'mod-root-child-1',
            name: 'Child Module',
            hierarchy: HIERARCHY.ROOT,
            navigationPosition: 2,
            navigationType: 'menu',
            translationKey: 'module.child',
            level: 2,
            parentId: 'mod-root-1',
            policySetting: {
              toJSON: () => ({
                canView: true,
              }),
            },
            children: [],
          },
        ],
      },
    ];

    const result = departmentService.filterAndStructureModules(modules, [HIERARCHY.ROOT]);

    expect(result[HIERARCHY.ROOT]).toHaveLength(1);

    const parent = result[HIERARCHY.ROOT][0];
    expect(parent.children).toHaveLength(1);

    expect(parent.children[0]).toMatchObject({
      id: 'mod-root-child-1',
      name: 'Child Module',
      parentId: 'mod-root-1',
    });
  });
});
