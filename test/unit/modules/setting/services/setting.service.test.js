import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as SettingService from '#src/modules/setting/services/setting.service.js';
import { LocalisationConstant, SettingConstant } from '#src/modules/setting/constants/index.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';
import { SettingRepository } from '#src/modules/setting/repository/index.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

vi.mock('#src/modules/setting/repository/index.js');
vi.mock('#src/modules/setting/services/index.js');
vi.mock('#src/utils/db-transaction.util.js');

// Mock CoreError
vi.mock('#src/modules/core/errors/index.js', () => ({
  CoreError: {
    dataNotFound: vi
      .fn()
      .mockImplementation(
        (data, attribute, value) => new Error(`No ${data} found with ${attribute}: ${value}`),
      ),
  },
}));

// Mock SettingError
vi.mock('#src/modules/setting/errors/index.js', () => ({
  SettingError: {
    unsupportedInfo: vi
      .fn()
      .mockImplementation(
        (category) => new Error(`Unsupported info operation for category: ${category}`),
      ),
  },
}));

const {
  SETTING_CATEGORIES,
  APPEARANCE_MODES,
  TOAST_POSITIONS,
  ALERT_POSITIONS,
  SESSION_LIFETIME_HOURS,
  PASSWORD_EXPIRY_DAYS,
  PASSWORD_REUSE_COUNT,
  PASSWORD_MAXIMUM_ATTEMPTS,
  TWO_FACTOR_SESSION_TIMEOUT_DAYS,
  RECORDS_PER_PAGE,
  DATE_FORMATS,
  TIME_FORMATS,
} = SettingConstant;
const { LANGUAGE, REGION } = LocalisationConstant.LOCALISATION_CATEGORIES;

describe('Setting Service', () => {
  let mockRequest;

  beforeEach(() => {
    mockRequest = {
      params: { category: SETTING_CATEGORIES.PERSONAL },
      entity: { id: 'entity1', accessLevel: 'user' },
      server: {
        psql: {
          CustomSetting: {
            findAll: vi.fn(),
          },
        },
      },
      authInfo: { id: 'user1' },
      body: {},
      query: {},
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should return settings for a given category and entity', async () => {
      const mockSettings = [
        { id: 'setting1', field: 'language', value: 'en' },
        { id: 'setting2', field: 'region', value: 'US' },
      ];

      SettingRepository.findByCategoryAndAccess.mockResolvedValue(mockSettings);

      const query = {
        'filter_customSettings.entityId_eq': mockRequest.authInfo.id,
        filter_category_eq: SETTING_CATEGORIES.PERSONAL,
        filter_accessLevels_overlap: mockRequest.entity.accessLevel,
      };

      const result = await SettingService.index(mockRequest, SETTING_CATEGORIES.PERSONAL);

      expect(SettingRepository.findByCategoryAndAccess).toHaveBeenCalledWith(
        mockRequest.server,
        query,
      );
      expect(result).toEqual(mockSettings);
    });

    it('should throw CoreError.dataNotFound if no settings are found', async () => {
      SettingRepository.findByCategoryAndAccess.mockResolvedValue([]);

      await expect(SettingService.index(mockRequest, SETTING_CATEGORIES.PERSONAL)).rejects.toThrow(
        'No setting found with category: personal',
      );
    });

    it('should use entityId for non-PERSONAL categories', async () => {
      const nonPersonalCategory = SETTING_CATEGORIES.SAFETY;

      const mockSettings = [{ id: 'setting3', field: 'sessionTimeout', value: '60' }];

      SettingRepository.findByCategoryAndAccess.mockResolvedValue(mockSettings);

      const query = {
        'filter_customSettings.entityId_eq': mockRequest.entity.id,
        filter_category_eq: nonPersonalCategory,
        filter_accessLevels_overlap: mockRequest.entity.accessLevel,
      };

      const result = await SettingService.index(mockRequest, nonPersonalCategory);

      expect(SettingRepository.findByCategoryAndAccess).toHaveBeenCalledWith(
        mockRequest.server,
        query,
      );

      expect(result).toEqual(mockSettings);
    });
  });

  describe('update', () => {
    it('should update settings for a given category', async () => {
      mockRequest.body = { language: 'fr', region: 'FR', version: 2 };
      const mockSettings = [
        { id: 'setting1', field: 'language', value: 'en' },
        { id: 'setting2', field: 'region', value: 'US' },
      ];

      SettingRepository.findByCategoryAndAccess.mockResolvedValue(mockSettings);
      withTransaction.mockImplementation((server, options, callback) =>
        callback({ transaction: 'mockTransaction' }),
      );

      await SettingService.update(mockRequest, SETTING_CATEGORIES.PERSONAL);

      expect(SettingRepository.upsertCustomSettings).toHaveBeenCalledWith(
        mockRequest.server,
        [
          { parentId: 'setting1', entityId: 'user1', value: 'fr', version: 2 },
          { parentId: 'setting2', entityId: 'user1', value: 'FR', version: 2 },
        ],
        {
          transaction: { transaction: 'mockTransaction' },
          authInfoId: 'user1',
        },
      );
    });

    it('should handle setting with customSettings defined but value missing', async () => {
      mockRequest.body = { region: 'SG', version: 1 };

      const mockSettings = [
        {
          id: 'setting2',
          field: 'region',
          customSettings: {}, // value is missing inside
        },
      ];

      SettingRepository.findByCategoryAndAccess.mockResolvedValue(mockSettings);
      withTransaction.mockImplementation((server, options, callback) =>
        callback({ transaction: 'mockTransaction' }),
      );

      await SettingService.update(mockRequest, SETTING_CATEGORIES.PERSONAL);

      expect(SettingRepository.upsertCustomSettings).toHaveBeenCalledWith(
        mockRequest.server,
        [
          {
            parentId: 'setting2',
            entityId: 'user1',
            value: 'SG',
            version: 1,
          },
        ],
        {
          transaction: { transaction: 'mockTransaction' },
          authInfoId: 'user1',
        },
      );
    });

    it('should use entityId for non-PERSONAL category during update', async () => {
      mockRequest.body = { language: 'en', version: 1 };
      mockRequest.params.category = SETTING_CATEGORIES.SAFETY;

      const mockSettings = [
        {
          id: 'setting1',
          field: 'language',
          customSettings: { value: 'fr' },
        },
      ];

      SettingRepository.findByCategoryAndAccess.mockResolvedValue(mockSettings);

      withTransaction.mockImplementation((server, options, callback) =>
        callback({ transaction: 'mockTransaction' }),
      );

      await SettingService.update(mockRequest, SETTING_CATEGORIES.SAFETY);

      expect(SettingRepository.upsertCustomSettings).toHaveBeenCalledWith(
        mockRequest.server,
        [
          {
            parentId: 'setting1',
            entityId: mockRequest.entity.id, // entity1
            value: 'en',
            version: 1,
          },
        ],
        {
          transaction: { transaction: 'mockTransaction' },
          authInfoId: 'user1',
        },
      );
    });

    it('should return empty result when no matching fields to update', async () => {
      mockRequest.body = { unrelatedField: 'noop', version: 3 };

      const mockSettings = [
        { id: 'setting1', field: 'language', customSettings: { value: 'en' } },
        { id: 'setting2', field: 'region', customSettings: { value: 'US' } },
      ];

      SettingRepository.findByCategoryAndAccess.mockResolvedValue(mockSettings);
      withTransaction.mockImplementation((server, options, callback) =>
        callback({ transaction: 'mockTx' }),
      );

      const result = await SettingService.update(mockRequest, SETTING_CATEGORIES.PERSONAL);

      expect(result).toEqual({
        result: {},
        audit: {
          beforeState: {},
          afterState: {},
          fieldsChanged: [],
        },
      });

      expect(SettingRepository.upsertCustomSettings).toHaveBeenCalledWith(mockRequest.server, [], {
        transaction: { transaction: 'mockTx' },
        authInfoId: 'user1',
      });
    });
  });

  describe('list', () => {
    it('should return personal options for personal category', async () => {
      LocalisationService.generateDropdown
        .mockResolvedValueOnce([
          { id: 'en', name: 'English' },
          { id: 'fr', name: 'French' },
        ])
        .mockResolvedValueOnce([
          { id: 'US', name: 'United States' },
          { id: 'FR', name: 'France' },
        ]);

      const result = await SettingService.list(mockRequest, mockRequest.params.category);

      expect(LocalisationService.generateDropdown).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        appearanceModes: Object.values(APPEARANCE_MODES),
        preferences: RECORDS_PER_PAGE,
        dateFormat: Object.values(DATE_FORMATS),
        timeFormat: Object.values(TIME_FORMATS),
        [LANGUAGE]: [
          { id: 'en', name: 'English' },
          { id: 'fr', name: 'French' },
        ],
        [REGION]: [
          { id: 'US', name: 'United States' },
          { id: 'FR', name: 'France' },
        ],
      });
    });

    it('should return safety options for safety category', async () => {
      mockRequest.params.category = SETTING_CATEGORIES.SAFETY;

      const result = await SettingService.list(mockRequest, mockRequest.params.category);

      expect(result).toEqual({
        sessionLifetimeHours: SESSION_LIFETIME_HOURS,
        passwordExpiryDays: PASSWORD_EXPIRY_DAYS,
        passwordReuseCount: PASSWORD_REUSE_COUNT,
        passwordMaximumAttempts: PASSWORD_MAXIMUM_ATTEMPTS,
        twoFactorSessionTimeoutDays: TWO_FACTOR_SESSION_TIMEOUT_DAYS,
      });
    });

    it('should return themes options for themes category', async () => {
      mockRequest.params.category = SETTING_CATEGORIES.THEMES;

      const result = await SettingService.list(mockRequest, mockRequest.params.category);

      expect(result).toEqual({
        toastPositions: Object.values(TOAST_POSITIONS),
        alertPositions: Object.values(ALERT_POSITIONS),
      });
    });

    it('should throw SettingError.unsupportedInfo for unsupported category', async () => {
      mockRequest.params.category = 'unsupported';

      await expect(SettingService.list(mockRequest)).rejects.toThrow(
        'Unsupported info operation for category: unsupported',
      );
    });
  });
});
