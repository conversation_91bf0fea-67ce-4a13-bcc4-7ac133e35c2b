import { describe, expect, it } from 'vitest';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { settingError } from '#src/modules/setting/errors/setting.error.js';

describe('Setting Error Module', () => {
  it('should create accessDenied error with correct properties', () => {
    const category = 'safety';
    const error = settingError.accessDenied({ category });

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('52403');
    expect(error.message).toBe(`error.settings.sentence.accessDenied`);
    expect(error.metaData).toEqual({ translationParams: { category } });
    expect(error.statusCode).toBe(403);
    expect(error.name).toBe(`${CoreConstant.MODULE_NAMES.SETTING}ModuleError`);
  });

  it('should create unsupportedInfo error with correct properties', () => {
    const category = 'safety';
    const error = settingError.unsupportedInfo({ category });

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('52422');
    expect(error.message).toBe(`error.settings.sentence.unsupportedInfo`);
    expect(error.metaData).toEqual({ translationParams: { category } });
    expect(error.statusCode).toBe(422);
    expect(error.name).toBe(`${CoreConstant.MODULE_NAMES.SETTING}ModuleError`);
  });
});
