import type { AlertMap } from '@/constants/alert.constant';

/**
 * A mapping of setting error codes to their corresponding alert data.
 * This is used to provide user-friendly error messages based on specific setting errors.
 */
export const settingAlertMap: AlertMap = {
  '52400': { severity: 'error', title: 'error.label.invalidData' },
  '52403': { severity: 'error', title: 'error.label.accessDenied' },
  '52404': { severity: 'error', title: 'error.label.dataNotFound' },
  '52422': { severity: 'error', title: 'error.label.unsupportedInfo' },
  default: { severity: 'error', title: 'error.label.failed', message: 'error.sentence.internal' },
};
