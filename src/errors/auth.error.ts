import type { AlertMap } from '@/constants/alert.constant';

/**
 * A mapping of authentication error codes to their corresponding alert data.
 * This is used to provide user-friendly error messages based on specific authentication errors.
 */
export const authAlertMap: AlertMap = {
  '20001': { severity: 'error', title: 'error.auth.sentence.invalidCredentials' },
  '20002': { severity: 'error', title: 'error.auth.sentence.sessionExpired' },
  '20003': { severity: 'error', title: 'error.auth.sentence.accountInactive' },
  '20004': { severity: 'error', title: 'error.auth.sentence.suspiciousActivityDetected' },
  '20005': { severity: 'error', title: 'error.auth.sentence.accountLockedManyAttempts' },
  '20006': { severity: 'error', title: 'error.auth.sentence.accountLocked' },
  '20007': { severity: 'info', title: 'error.auth.sentence.2faSetupRequired' },
  '20008': { severity: 'info', title: 'error.auth.sentence.2faRequired' },
  '20009': { severity: 'error', title: 'error.auth.sentence.googleLoginFailed' },
  default: {
    severity: 'error',
    title: 'error.label.failed',
    message: 'error.auth.sentence.somethingWentWrongPleaseTryAgain',
  },
};
