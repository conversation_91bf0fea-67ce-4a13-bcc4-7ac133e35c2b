import { ApiError, errorService } from '@/services/error/error.service';
import axios, { AxiosError } from 'axios';

const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 60000,
});

axiosInstance.interceptors.request.use(
  (config) => {
    const newConfig = config;

    // Add JWT token
    const jwtToken = localStorage.getItem('jwtToken');
    if (jwtToken) {
      newConfig.headers['Authorization'] = `Bearer ${jwtToken}`;
    }

    // Add language
    const language = localStorage.getItem('i18nextLng') ?? 'en';
    newConfig.headers['Accept-Language'] = language;

    // Add access id
    if (typeof window !== 'undefined') {
      const accessIdMatch = /^\/(\d{12})\//.exec(window.location.pathname);
      if (accessIdMatch) {
        newConfig.headers['x-access-id'] = accessIdMatch[1];
      }
    }

    return newConfig;
  },
  (error) => {
    return Promise.reject(error as AxiosError);
  }
);

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Convert all errors to ApiError so service layers can be simple
    const apiError = errorService.handleError(error);
    return Promise.reject(
      new ApiError(apiError.message ?? 'Unknown error', apiError.errorCode ?? 'UNKNOWN')
    );
  }
);

export default axiosInstance;
