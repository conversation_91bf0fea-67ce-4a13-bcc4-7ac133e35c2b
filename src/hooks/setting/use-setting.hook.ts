import {
  settingService,
  type Setting,
  type SettingCategory,
  type SettingFormData,
  type SettingOption,
} from '@/services/settings';
import { type ApiResponse } from '@/types/api-responses.type';
import {
  useMutation,
  useQuery,
  useQueryClient,
  type UseMutationOptions,
  type UseMutationResult,
  type UseQueryOptions,
  type UseQueryResult,
} from '@tanstack/react-query';

/**
 * Custom TanStack Query hook to fetch settings for a specific category.
 *
 * @param category - The setting category to fetch settings for
 * @param options - Optional React Query options
 * @returns UseQueryResult containing the API response with settings data
 */
export const useGetSettings = (
  category: SettingCategory,
  options?: Omit<UseQueryOptions<ApiResponse<Setting[]>, Error>, 'queryKey' | 'queryFn'>
): UseQueryResult<ApiResponse<Setting[]>, Error> => {
  return useQuery({
    queryKey: ['settings', category],
    queryFn: () => settingService.getSettings(category),
    refetchOnWindowFocus: false,
    refetchInterval: false,
    ...options,
  });
};

/**
 * Custom TanStack Query hook to fetch setting options for a specific category.
 *
 * @param category - The setting category to fetch settings for
 * @param options - Optional React Query options
 * @returns UseQueryResult containing the API response with setting options data
 */
export const useGetSettingOptions = (
  category: SettingCategory,
  options?: Omit<UseQueryOptions<ApiResponse<SettingOption>, Error>, 'queryKey' | 'queryFn'>
): UseQueryResult<ApiResponse<SettingOption>, Error> => {
  return useQuery({
    queryKey: ['settingOptions', category],
    queryFn: () => settingService.getSettingOptions(category),
    refetchOnWindowFocus: false,
    refetchInterval: false,
    ...options,
  });
};

/**
 * Custom TanStack Query hook to update settings for a specific category.
 * Automatically invalidates the settings cache for the category after successful update.
 *
 * @param category - The setting category to update ('personal', 'safety', 'themes')
 * @param options - Optional React Query mutation options (excluding mutationFn and onSuccess)
 * @returns UseMutationResult for updating settings with the appropriate form data type
 *
 * @example
 * ```typescript
 * const { mutate } = useUpdateSetting('themes');
 *
 * // Usage
 * mutate({ name: 'John Doe' });
 * ```
 */
export const useUpdateSetting = (
  category: SettingCategory,
  options?: Omit<
    UseMutationOptions<ApiResponse, Error, SettingFormData>,
    'mutationFn' | 'onSuccess'
  >
): UseMutationResult<ApiResponse, Error, SettingFormData> => {
  const queryClient = useQueryClient();

  const getMutationFn = (category: SettingCategory) => {
    switch (category) {
      case 'personal':
        return settingService.updatePersonalSetting;
      case 'safety':
        return settingService.updateSafetySetting;
      case 'themes':
        return settingService.updateThemesSetting;
      default:
        throw new Error(`Invalid category: ${category}`);
    }
  };

  return useMutation({
    mutationFn: getMutationFn(category) as (data: SettingFormData) => Promise<ApiResponse>,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['settings', category],
      });
    },
    ...options,
  });
};
