import { USER } from '@/constants';
import { transformText } from '@/utils/text-transform.util';
import type { TFunction } from 'i18next';
import { z as zod } from 'zod';

/**
 * Normal login form validation schema
 *
 * Validates access id, username and password fields for the normal login form.
 * Pass a translation function (t) as an argument.
 */
export const normalLoginSchema = (t: TFunction<'translation'>) => {
  const usernameLabel = transformText(t('common.label.username'), 'sentenceCase');
  const passwordLabel = transformText(t('common.label.password'), 'sentenceCase');
  const accessIdLabel = t('common.label.accessId');

  const ACCESS_ID_RE = new RegExp(`^\\d{${USER.accessId.exactLength}}$`);

  const accessIdExactMsg = t('error.validation.sentence.exactLength', {
    attribute: accessIdLabel,
    limit: USER.accessId.exactLength,
    type: t('common.label.number'),
  });

  const usernameMinMsg = t('error.validation.sentence.minLength', {
    attribute: usernameLabel,
    limit: USER.username.minLength,
  });
  const usernameMaxMsg = t('error.validation.sentence.maxLength', {
    attribute: usernameLabel,
    limit: USER.username.maxLength,
  });

  const passwordRequiredMsg = t('error.validation.sentence.required', {
    attribute: passwordLabel,
  });
  const passwordMinMsg = t('error.validation.sentence.minLength', {
    attribute: passwordLabel,
    limit: USER.password?.minLength ?? 6, // fallback if not defined
  });

  // Treat empty string as "not provided" for accessId, otherwise enforce exact length digits
  const accessIdSchema = zod
    .string()
    .trim()
    .optional()
    .refine((val) => !val || ACCESS_ID_RE.test(val), { message: accessIdExactMsg });

  return zod
    .object({
      accessId: accessIdSchema,
      username: zod
        .string()
        .trim()
        .min(USER.username.minLength, { message: usernameMinMsg })
        .max(USER.username.maxLength, { message: usernameMaxMsg }),
      password: zod
        .string()
        .trim()
        .nonempty({ message: passwordRequiredMsg })
        .min(USER.password?.minLength ?? 1, { message: passwordMinMsg }),
    })
    .strict();
};

/**
 * Normal login form values type
 */
// Type must match the shape returned by the schema
export type NormalLoginFormSchema = {
  accessId?: string; // optional to match schema
  username: string;
  password: string;
};

/**
 * Default values for the normal login form
 */
export const defaultNormalLoginValues: NormalLoginFormSchema = {
  accessId: '',
  username: '',
  password: '',
};

/**
 * OAuth login form validation schema
 *
 * Validates access id for the OAuth login form.
 */
export const oAuthLoginSchema = (t: TFunction<'translation'>) => {
  const accessIdLabel = t('common.label.accessId');
  const ACCESS_ID_RE = new RegExp(`^\\d{${USER.accessId.exactLength}}$`);

  const accessIdExactMsg = t('error.validation.sentence.exactLength', {
    attribute: accessIdLabel,
    limit: USER.accessId.exactLength,
    type: t('common.label.number'),
  });

  const accessIdSchema = zod
    .string()
    .trim()
    .optional()
    .refine((val) => !val || ACCESS_ID_RE.test(val), { message: accessIdExactMsg });

  return zod
    .object({
      accessId: accessIdSchema,
    })
    .strict();
};

/**
 * OAuth login form values type
 */
export type OAuthLoginFormSchema = {
  accessId?: string; // optional to match schema
};

/**
 * Default values for the OAuth login form
 */
export const defaultOAuthLoginValues: OAuthLoginFormSchema = {
  accessId: '',
};
