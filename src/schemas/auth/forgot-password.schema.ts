import { USER } from '@/constants';
import { transformText } from '@/utils/text-transform.util';
import type { TFunction } from 'i18next';
import { z as zod } from 'zod';

/**
 * Forgot password form validation schema
 *
 * Validates email field for the forgot password form.
 */
export const forgotPasswordSchema = (t: TFunction) => {
  const usernameLabel = transformText(t('common.label.username'), 'sentenceCase');
  const emailLabel = transformText(t('common.label.email'), 'sentenceCase');

  const usernameMinMsg = t('error.validation.sentence.minLength', {
    attribute: usernameLabel,
    limit: USER.username.minLength,
  });
  const usernameMaxMsg = t('error.validation.sentence.maxLength', {
    attribute: usernameLabel,
    limit: USER.username.maxLength,
  });

  const emailRequiredMsg = t('error.validation.sentence.required', {
    attribute: emailLabel,
  });
  const emailInvalidMsg = t('error.validation.sentence.invalid', {
    attribute: emailLabel,
  });

  return zod
    .object({
      username: zod
        .string()
        .trim()
        .min(USER.username.minLength, { message: usernameMinMsg })
        .max(USER.username.maxLength, { message: usernameMaxMsg }),
      email: zod
        .string()
        .trim()
        .nonempty({ message: emailRequiredMsg }) // ← required
        .email({ message: emailInvalidMsg }),
    })
    .strict();
};

/**
 * Forgot password form values type
 */
export type ForgotPasswordFormValues = {
  username: string;
  email: string;
};

/**
 * Default values for the forgot password form
 */
export const defaultForgotPasswordValues: ForgotPasswordFormValues = {
  username: '',
  email: '',
};
