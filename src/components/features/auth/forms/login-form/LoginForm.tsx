import { FormAlert } from '@/components/base/feedback/alert';
import { RouterLink } from '@/components/base/navigation/router-link';
import { Typography } from '@/components/base/typography';
import { getAlertFromErrorCode } from '@/constants/alert.constant';
import { authAlertMap } from '@/errors/auth.error';
import { useAuth } from '@/hooks/auth/use-auth.hook';
import { useRouter } from '@/hooks/navigation/use-router.hook';
import ROUTES from '@/router/routes';
import {
  defaultNormalLoginValues,
  defaultOAuthLoginValues,
  normalLoginSchema,
  oAuthLoginSchema,
  type NormalLoginFormSchema,
  type OAuthLoginFormSchema,
} from '@/schemas/auth';
import { authService } from '@/services/auth/auth.service';
import { isSuccessResponse } from '@/types/api-responses.type';
import type { AlertState } from '@/types/generic.type';
import { zodResolver } from '@hookform/resolvers/zod';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { oauthProviderConfig } from 'config/oauth.config';
import type { TFunction } from 'i18next';
import Script from 'next/script';
import { useCallback, useEffect, useState, type JSX } from 'react';
import { useForm, type UseFormSetError } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  AccessIdField,
  DemoCredentials,
  FormContainer,
  FormHeader,
  OAuthButton,
  PasswordField,
  SubmitButton,
  UsernameField,
} from '../shared/AuthFormComponents';
import { oAuthProviders } from './LoginForm.type';

type OAuthProviderKey = keyof typeof oauthProviderConfig;

const processLoginError = ({
  response,
  t,
  setAlert,
  setErrorFn,
  setPendingApproval,
}: {
  response: any;
  t: TFunction<'translation'>;
  setAlert: (alert: AlertState) => void;
  setErrorFn?: UseFormSetError<NormalLoginFormSchema>;
  setPendingApproval: (value: string) => void;
}) => {
  const alertData = getAlertFromErrorCode(t, response.errorCode, response.message, authAlertMap);

  setAlert(alertData);

  if (setErrorFn) {
    setErrorFn('root', {
      type: 'server',
      message: response.message,
    });
  }

  if (response.errorCode === '20010') {
    setPendingApproval('true');
  }
};

/**
 * LoginForm
 *
 * A comprehensive authentication form component that handles both normal (username/password)
 * login and OAuth (e.g., Google) login flows using React Hook Form and Zod for validation.
 *
 * Features:
 * - Normal login with access ID, username, and password fields
 * - OAuth login with support for provider-specific initialisation (currently Google)
 * - Validation and error handling with zod schemas
 * - Session checking and server-side rehydration via `checkSession` and `router.refresh()`
 * - Handles special state for "pending approval" by checking and redirecting if needed
 * - Responsive layout using MUI Grid and conditional divider orientation
 * - Displays alert messages and demo credentials for test environments
 *
 * Behaviour:
 * - On successful login, user session is refreshed and page data revalidated
 * - On specific error code (e.g. 20010), the component flags the user as pending approval
 *   and schedules redirection to the `/pending-approval` page
 * - `localStorage` is used to persist the "pending approval" status across renders
 * - OAuth flow initialises the Google Identity SDK and handles credential callback
 *
 * Requirements:
 * - This component is intended to run on the client side (`'use client'`)
 * - Must be wrapped in a page/layout that supports client-side rendering
 *
 * @returns {JSX.Element} The rendered login form including normal and OAuth login sections.
 *
 * @example
 * ```tsx
 * <LoginForm />
 * ```
 */
export const LoginForm = (): JSX.Element => {
  const router = useRouter();
  const { checkSession } = useAuth();
  const { t } = useTranslation();

  const [alert, setAlert] = useState<AlertState>(null);
  const [isPending, setIsPending] = useState<boolean>(false);
  const [pendingApproval, setPendingApproval] = useState<string>('');
  const [shouldRender, setShouldRender] = useState<boolean>(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const dividerOrientation = isMobile ? 'horizontal' : 'vertical';

  // ---------- Normal Login Form ----------
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<NormalLoginFormSchema>({
    defaultValues: defaultNormalLoginValues,
    resolver: zodResolver(normalLoginSchema(t)),
  });

  // ---------- OAuth Login Form ----------
  const {
    register: registerOAuth,
    getValues: getOAuthValues,
    trigger: triggerOAuth,
    formState: { errors: errorsOAuth },
  } = useForm<OAuthLoginFormSchema>({
    defaultValues: defaultOAuthLoginValues,
    resolver: zodResolver(oAuthLoginSchema(t)),
    mode: 'onTouched',
  });

  useEffect(() => {
    const stored = localStorage.getItem('pendingApproval') ?? '';
    setPendingApproval(stored);
  }, []);

  // In real application, you would fetch the user's pending approval status from your backend
  // If the user is pending for approval, go to the approval page
  useEffect(() => {
    if (pendingApproval === 'true') {
      router.push('/pending-approval');
    } else {
      setShouldRender(true);
    }
  }, [pendingApproval, router]);

  // ---------- Handlers ----------
  const handleLoginResponse = useCallback(
    async (
      response: Awaited<ReturnType<typeof authService.signInWithPassword>>,
      setErrorFn?: typeof setError
    ): Promise<void> => {
      const isSuccess = isSuccessResponse(response);

      setIsPending(false);
      // Scroll to top to make sure alert is visible
      window.scrollTo({ top: 0, behavior: 'smooth' });

      if (!isSuccess) {
        processLoginError({ response, t, setAlert, setErrorFn, setPendingApproval });
        return;
      }

      setAlert({
        severity: 'success',
        title: t('auth.sentence.loginSuccessful'),
        message: response.message,
      });

      await checkSession();
      router.refresh();
    },
    [checkSession, router, setIsPending, setAlert, t]
  );

  const onSubmit = useCallback(
    async (values: NormalLoginFormSchema): Promise<void> => {
      setIsPending(true);
      setAlert(null);

      const response = await authService.signInWithPassword(values);
      await handleLoginResponse(response, setError);
    },
    [setError, handleLoginResponse]
  );

  const handleOAuthLogin = async (providerKey: OAuthProviderKey) => {
    setIsPending(true);
    setAlert(null);

    // Trigger Zod validation manually
    const isValid = await triggerOAuth();
    if (!isValid) {
      setIsPending(false);
      return;
    }

    const provider = oauthProviderConfig[providerKey];
    if (!provider) {
      console.error(`OAuth provider '${providerKey}' not supported`);
      setIsPending(false);
      return;
    }

    const { accessId } = getOAuthValues();

    try {
      await provider.sdkInit();

      // Override the callback dynamically
      if (providerKey === 'google') {
        if (window.google?.accounts?.id) {
          window.google.accounts.id.initialize({
            client_id: provider.clientId,
            callback: (credentialResponse) => {
              const idToken = credentialResponse.credential;

              (async () => {
                const response = await authService.signInWithGoogle({ accessId, idToken });
                await handleLoginResponse(response, setError);
              })();
            },
          });

          provider.prompt();
        } else {
          console.error('Google Identity SDK not loaded');
          setIsPending(false);
        }
      }
    } catch (error) {
      console.error(`Failed to initialize ${provider.name} OAuth:`, error);
      setIsPending(false);
    }
  };

  if (!shouldRender) return <></>;

  return (
    <>
      <Script src="https://accounts.google.com/gsi/client" />
      {/* Alert bars */}
      <Container maxWidth="lg">
        <Grid
          container
          spacing={1}
          py={3}
        >
          {alert && (
            <Grid size={12}>
              <FormAlert
                severity={alert.severity}
                title={alert.title}
                message={alert.message}
              />
            </Grid>
          )}

          {/* Demo Credentials */}
          <DemoCredentials />
        </Grid>
      </Container>

      <FormHeader
        title={t('common.label.signIn')}
        subtitle={t('common.sentence.accessAccountContinueJourney')}
      />
      <FormContainer>
        <Box
          display="flex"
          flexDirection={{ xs: 'column', md: 'row' }}
          alignItems="center"
          justifyContent="center"
          sx={{
            gap: {
              sm: 8,
              md: 0,
              lg: 8,
            },
          }}
        >
          {/* Normal Login Form */}
          <form onSubmit={handleSubmit(onSubmit)}>
            <Container
              maxWidth="sm"
              sx={{ px: 2, py: 3 }}
            >
              <Grid
                container
                spacing={1}
              >
                {/* Access ID Field */}
                <AccessIdField
                  register={register}
                  errors={errors}
                  isPending={isPending}
                  dataTestId="accessId"
                />

                {/* Username Field */}
                <UsernameField
                  register={register}
                  errors={errors}
                  isPending={isPending}
                />

                {/* Password Field */}
                <PasswordField
                  register={register}
                  errors={errors}
                  isPending={isPending}
                />

                {/* Forgot Password */}
                <Link
                  component={RouterLink}
                  href={ROUTES.AUTH['RESET_PASSWORD']}
                  underline="hover"
                  data-testid="forgotPassword"
                >
                  <Typography caseTransform="sentenceCase">
                    {t('common.label.forgotPassword')}
                  </Typography>
                </Link>

                {/* Submit Button */}
                <SubmitButton
                  label={t('common.label.signIn')}
                  isPending={isPending}
                />
              </Grid>
            </Container>
          </form>

          {/* Divider */}
          <Divider
            orientation={dividerOrientation}
            flexItem
            aria-orientation={dividerOrientation}
          >
            <Typography caseTransform="uppercase">{t('common.label.or')}</Typography>
          </Divider>

          {/* OAuth Providers */}
          <Container
            maxWidth="sm"
            sx={{ px: 2, py: 3 }}
          >
            <Grid
              container
              spacing={1}
            >
              {/* Access ID Field */}
              <AccessIdField
                register={registerOAuth}
                errors={errorsOAuth}
                isPending={isPending}
                dataTestId="oauthAccessId"
              />

              {/* OAuth Button */}
              {oAuthProviders.map((provider) => (
                <OAuthButton
                  key={provider.id}
                  label={t('common.label.signInWithProvider', { provider: provider.name })}
                  logo={provider.logo}
                  altText={provider.name}
                  isPending={isPending}
                  onClick={() => handleOAuthLogin(provider.name as OAuthProviderKey)}
                ></OAuthButton>
              ))}
            </Grid>
          </Container>
        </Box>
      </FormContainer>
    </>
  );
};

export default LoginForm;
