// AuthFormComponents.stories.tsx
import i18n from '@/providers/i18n/i18n';
import ROUTES from '@/router/routes';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import React from 'react';
import { useForm } from 'react-hook-form';
import {
  AccessIdField,
  DemoCredentials,
  EmailField,
  FormHeader,
  OAuthButton,
  PasswordField,
  RedirectButton,
  RefreshButton,
  SubmitButton,
  UsernameField,
  type FormHeaderProps,
} from './AuthFormComponents';
import { type InputFieldProps } from './AuthFormComponents.type';

const meta: Meta<FormHeaderProps> = {
  title: 'Components/features/auth/forms/shared/AuthFormComponent',
  component: FormHeader,
  parameters: {
    layout: 'centered',
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * A higher-order component that wraps form fields with a common form structure.
 * It provides a form context using `react-hook-form` and handles form state.
 *
 * @param props - The component props.
 * @param props.children - The form fields to be wrapped.
 *
 * @returns A React component that renders a form with the provided form fields.
 */
const FormWrapper = ({ Component }: { Component: React.ComponentType<InputFieldProps> }) => {
  const {
    register,
    formState: { errors },
  } = useForm({
    defaultValues: {
      accessId: '',
      username: '',
      email: '',
      password: '',
    },
  });

  return (
    <form>
      <Component
        register={register}
        errors={errors}
        isPending={false}
      />
    </form>
  );
};

export const Header: Story = {
  args: {
    title: 'Sign in',
    subtitle: 'Access your account and continue your journey',
    align: 'center',
  },
  render: (args) => <FormHeader {...args} />,
};

export const AccessId: Story = {
  render: () => <FormWrapper Component={AccessIdField} />,
};

export const Username: Story = {
  render: () => <FormWrapper Component={UsernameField} />,
};

export const Email: Story = {
  render: () => <FormWrapper Component={EmailField} />,
};

export const Password: Story = {
  render: () => <FormWrapper Component={PasswordField} />,
};

export const Submit: Story = {
  render: () => (
    <SubmitButton
      label={i18n.t('common.label.signIn')}
      isPending={false}
    />
  ),
};

export const RefreshBtn: Story = {
  render: () => (
    <RefreshButton
      label="Refresh"
      // eslint-disable-next-line no-alert
      onClick={() => alert('Mock refresh clicked!')}
    />
  ),
};

export const DemoLabel: Story = {
  render: () => <DemoCredentials />,
};

export const OAuth: Story = {
  render: () => (
    <OAuthButton
      label={i18n.t('common.label.signInWithProvider', { provider: 'Google' })}
      logo="/placeholders/logo/google-icon.svg"
      altText="Google"
      isPending={false}
    />
  ),
};

export const Redirect: Story = {
  render: () => (
    <RedirectButton
      label={i18n.t('common.label.returnToSignIn')}
      startIcon={<KeyboardBackspaceRoundedIcon />}
      redirectTo={ROUTES.AUTH['LOGIN']}
      isPending={false}
    />
  ),
};
