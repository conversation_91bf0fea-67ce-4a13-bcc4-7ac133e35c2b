import { FormAlert } from '@/components/base/feedback/alert/FormAlert';
import { getAlertFromErrorCode } from '@/constants';
import { authAlertMap } from '@/errors/auth.error';
import ROUTES from '@/router/routes';
import {
  defaultForgotPasswordValues,
  forgotPasswordSchema,
  type ForgotPasswordFormValues,
} from '@/schemas/auth';
import { authService } from '@/services/auth/auth.service';
import { isSuccessResponse } from '@/types/api-responses.type';
import type { AlertState } from '@/types/generic.type';
import { zodResolver } from '@hookform/resolvers/zod';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import { Container, Grid } from '@mui/material';
import Divider from '@mui/material/Divider';
import { useCallback, useState, type JSX } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  <PERSON>mo<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FormContainer,
  FormHeader,
  RedirectButton,
  SubmitButton,
  UsernameField,
} from '../../shared/AuthFormComponents';

/**
 * ForgotPasswordForm is a component that renders a form for users to reset their password.
 *
 * It includes:
 * - Username input field
 * - Email input field
 * - Form validation
 * - Error handling
 * - Link to return to sign in page
 *
 * @example
 * ```tsx
 * <ForgotPasswordForm />
 * ```
 */
export const ForgotPasswordForm = (): JSX.Element => {
  const [isPending, setIsPending] = useState<boolean>(false);
  const [alert, setAlert] = useState<AlertState>(null);

  const { t } = useTranslation();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormValues>({
    defaultValues: defaultForgotPasswordValues,
    resolver: zodResolver(forgotPasswordSchema(t)),
  });

  const onSubmit = useCallback(
    async (values: ForgotPasswordFormValues): Promise<void> => {
      setIsPending(true);

      const response = await authService.resetPassword(values);
      const isSuccess = isSuccessResponse(response);

      setIsPending(false);

      if (!isSuccess) {
        const alertData = getAlertFromErrorCode(
          t,
          response.errorCode,
          response.message,
          authAlertMap
        );

        setAlert(alertData);

        return;
      }

      setAlert({
        severity: 'success',
        title: 'requested successfully',
        message: response.message,
      });

      // After successful requested OTP, redirect to OTP verification page
      // router.push('/verify-otp');
    },
    [setAlert, t]
  );

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Container maxWidth="sm">
        <Grid
          container
          spacing={1}
          py={3}
        >
          {/* Response message */}
          {alert && (
            <Grid size={12}>
              <FormAlert
                severity={alert.severity}
                title={alert.title}
                message={alert.message}
              />
            </Grid>
          )}

          {/* Demo user */}
          <DemoUser />
        </Grid>
      </Container>
      <FormHeader
        title={t('common.label.forgotPassword')}
        subtitle={t('common.sentence.forgotPassword')}
        align="left"
      />
      <FormContainer>
        <Container maxWidth="sm">
          {/* Divider */}
          <Divider
            orientation="horizontal"
            flexItem
            aria-orientation="horizontal"
          ></Divider>
          <Grid
            container
            spacing={2}
            pt={6}
          >
            {/* Username Field */}
            <UsernameField
              register={register}
              errors={errors}
              isPending={isPending}
            />
            {/* Email Field */}
            <EmailField
              register={register}
              errors={errors}
              isPending={isPending}
            />

            {/* Submit Button */}
            <SubmitButton
              label={t('common.label.next')}
              isPending={isPending}
            />

            <Grid size={12}>
              {/* OTP information */}
              <FormAlert
                severity="info"
                message={t('common.sentence.resetPasswordOtpSentToEmail')}
              />
            </Grid>

            {/* Back to Login */}
            <RedirectButton
              label={t('common.label.returnToSignIn')}
              startIcon={<KeyboardBackspaceRoundedIcon />}
              redirectTo={ROUTES.AUTH['LOGIN']}
              isPending={isPending}
            />
          </Grid>
        </Container>
      </FormContainer>
    </form>
  );
};

export default ForgotPasswordForm;
