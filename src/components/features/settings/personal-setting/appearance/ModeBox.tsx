import DarkModeIcon from '@mui/icons-material/DarkMode';
import LightModeIcon from '@mui/icons-material/LightMode';
import { Box, useTheme } from '@mui/material';
import { type ModeBoxProps } from './ModeBox.type';

/**
 * ModeBox component displays visual representations of appearance modes (light, dark, system)
 * with appropriate icons and color schemes for appearance selection interfaces.
 */
export const ModeBox = (props: ModeBoxProps) => {
  const theme = useTheme();

  return (
    <>
      {props.variant !== 'system' && (
        <Box
          sx={{
            color:
              props.variant === 'dark' ? theme.palette.common.white : theme.palette.common.black,
            background:
              props.variant === 'dark' ? theme.palette.common.black : theme.palette.common.white,
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {props.variant === 'dark' ? <DarkModeIcon /> : <LightModeIcon />}
        </Box>
      )}
      {props.variant === 'system' && (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              flex: 1,
              background: theme.palette.common.white,
              color: theme.palette.common.black,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderTopRightRadius: '0 !important',
              borderBottomRightRadius: '0 !important',
            }}
          >
            <LightModeIcon />
          </Box>
          <Box
            sx={{
              flex: 1,
              background: theme.palette.common.black,
              color: theme.palette.common.white,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderTopLeftRadius: '0 !important',
              borderBottomLeftRadius: '0 !important',
            }}
          >
            <DarkModeIcon />
          </Box>
        </Box>
      )}
    </>
  );
};

export default ModeBox;
