import Stack from '@mui/material/Stack';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { ModeBox } from './ModeBox';

/**
 * A mode box component for personal settings apperance dropdown
 */
const meta = {
  title: 'Components/features/settings/personal-setting/appearance/ModeBox',
  component: ModeBox,
  parameters: { layout: 'centered' },
  argTypes: {
    variant: {
      control: 'select',
      options: ['system', 'light', 'dark'],
      description: 'The variant of the mode box',
    },
  },
  decorators: [
    (Story, { args }) => {
      return (
        <Stack
          spacing={1}
          width={180}
          height={100}
          sx={{
            border: '1px solid #ccc',
            borderRadius: 1,
            overflow: 'hidden',
          }}
        >
          <Story args={{ ...args }} />
        </Stack>
      );
    },
  ],
} satisfies Meta<typeof ModeBox>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default / System
 */
export const Default: Story = {
  args: {
    variant: 'system',
  },
};

/**
 * Light
 */
export const Light: Story = {
  args: {
    variant: 'light',
  },
};

/**
 * Dark
 */
export const Dark: Story = {
  args: {
    variant: 'dark',
  },
};
