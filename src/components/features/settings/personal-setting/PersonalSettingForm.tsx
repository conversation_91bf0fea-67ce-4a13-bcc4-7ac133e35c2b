'use client';

import { FormAccordion } from '@/components/application-ui/form-section/form-accordion';
import { RadioButtonCard } from '@/components/base/inputs/radio-button';
import { SelectBasic } from '@/components/base/inputs/select';
import { type Variant } from '@/components/features/settings/personal-setting/appearance';
import { ModeBox } from '@/components/features/settings/personal-setting/appearance/ModeBox';
import { getAlertFromErrorCode } from '@/constants/alert.constant';
import { LANGUAGE_OPTIONS } from '@/constants/languages.constant';
import { usePageAlertStore } from '@/contexts/page-alert/page-alert.context';
import { usePersonalSettingStore } from '@/contexts/setting/personal-setting.context';
import { settingAlertMap } from '@/errors/setting.error';
import {
  useGetSettingOptions,
  useGetSettings,
  useUpdateSetting,
} from '@/hooks/setting/use-setting.hook';
import { isApiError } from '@/services/error';
import { type PersonalFormData } from '@/services/settings';
import { isSuccessResponse } from '@/types/api-responses.type';
import { transformText } from '@/utils/text-transform.util';
import { useForm, useStore } from '@tanstack/react-form';
import { useEffect, useMemo, type JSX } from 'react';
import { useTranslation } from 'react-i18next';
import { PERSONAL_FORM } from './PersonalSettingForm.constant';
import { type PersonalSettingFormProps } from './PersonalSettingForm.type';

/**
 * PersonalSettingForm component renders a form for managing user personal settings
 * including appearance, language, preferences, and date/time configurations.
 */
export const PersonalSettingForm = ({
  setForm,
  setDirty,
  setLoading,
}: PersonalSettingFormProps): JSX.Element => {
  const { t } = useTranslation();

  // Global state
  const { showAlert } = usePageAlertStore((state) => state);
  const {
    version,
    appearance,
    recordPerPage,
    dateFormat,
    timeFormat,
    defaultLanguage,
    defaultTimezone,
    update,
  } = usePersonalSettingStore((state) => state);

  // Initiate data from API (To be removed when login fetch setting is implemented)
  const { data: personalData, isLoading: personalDataLoading } = useGetSettings('personal');
  useEffect(() => {
    if (personalData && isSuccessResponse(personalData)) {
      const updates: any = {};
      for (const setting of personalData.data!) {
        if (setting.customSettings?.value) {
          updates[setting.field] = setting.customSettings.value;
          updates.version = setting.customSettings.version;
        }
      }
      update(updates);
    }
  }, [personalData, update]);

  // Initialise form and form submit
  const { mutate, isPending: updatePersonalDataPending } = useUpdateSetting('personal');
  const form = useForm({
    defaultValues: {
      version: version,
      appearance: appearance,
      recordPerPage: recordPerPage,
      dateFormat: dateFormat,
      timeFormat: timeFormat,
      defaultLanguage: defaultLanguage,
      defaultTimezone: defaultTimezone,
    },
    onSubmit: async ({ value, formApi }) => {
      const updateData = {
        version: Number(value.version),
        appearance: value.appearance,
        recordPerPage: value.recordPerPage,
        dateFormat: value.dateFormat,
        timeFormat: value.timeFormat,
        defaultLanguage: value.defaultLanguage,
        defaultTimezone: value.defaultTimezone,
      };

      mutate(updateData, {
        onSuccess: (data) => {
          showAlert({
            title: transformText(t('common.label.success'), 'sentenceCase'),
            message: data.message!,
            severity: 'success',
          });
          update({ ...updateData, version: updateData.version + 1 });
          formApi.reset({ ...updateData, version: updateData.version + 1 });
        },
        onError: (error) => {
          let errorCode = '';
          if (isApiError(error)) errorCode = error.errorCode;

          const alertData = getAlertFromErrorCode(t, errorCode, error.message, settingAlertMap);
          showAlert({
            title: alertData.title,
            message: alertData.message,
            severity: alertData.severity,
          });
        },
      });
    },
  });

  // Set submit and reset functions
  useEffect(() => {
    setForm({
      submit: () => {
        form.handleSubmit().catch((error) => {
          console.error('Form submission error:', error);
        });
      },
      reset: form.reset,
    });
  }, [setForm, form]);

  // Track form isDirty state
  const isDirty = useStore(form.store, (state) => state.isDirty);
  useEffect(() => {
    setDirty(isDirty);
  }, [isDirty, setDirty]);

  // Populate form options
  const { data: personalOptions, isLoading: personalOptionsLoading } =
    useGetSettingOptions('personal');
  const dropdownOptions = useMemo(() => {
    let appearance;
    let recordPerPage;
    let dateFormat;
    let timeFormat;
    let defaultLanguage;
    let defaultTimezone;

    if (personalOptions && isSuccessResponse(personalOptions)) {
      // appearance radio options
      if (personalOptions.data!.appearanceModes) {
        appearance = personalOptions.data!.appearanceModes.map((item) => ({
          ...item,
          label: t(`settings.label.${item.value.toLowerCase()}`),
          children: <ModeBox variant={item.value as Variant} />,
        }));
      }

      // record per page select options
      if (personalOptions.data!.preferences) recordPerPage = personalOptions.data!.preferences;

      // date format select options
      if (personalOptions.data!.dateFormat) dateFormat = personalOptions.data!.dateFormat;

      // time format select options
      if (personalOptions.data!.timeFormat) timeFormat = personalOptions.data!.timeFormat;

      // language select options
      if (personalOptions.data!.language) {
        // get supported language options
        const supportedLanguageCodes = new Set(
          Object.values(LANGUAGE_OPTIONS).map((option) => option.code)
        );

        // filter out unsupported languages
        defaultLanguage = personalOptions.data!.language.filter((item) =>
          supportedLanguageCodes.has(item.code!)
        );
      }

      // timezone select options
      if (personalOptions.data!.region) defaultTimezone = personalOptions.data!.region;
    }

    return {
      appearance: appearance || [],
      recordPerPage: recordPerPage || [],
      dateFormat: dateFormat || [],
      timeFormat: timeFormat || [],
      defaultLanguage: defaultLanguage || [],
      defaultTimezone: defaultTimezone || [],
    };
  }, [personalOptions, t]);

  // Track API loading state
  const isLoading = personalDataLoading || personalOptionsLoading || updatePersonalDataPending;
  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading, setLoading]);

  return (
    <form>
      {PERSONAL_FORM.map((accordion) => {
        return (
          <FormAccordion
            key={accordion.title}
            defaultExpanded={accordion.defaultExpanded}
            title={t(accordion.title)}
            description={t(accordion.description)}
          >
            {accordion.formItems.map((item) => {
              return (
                <form.Field
                  key={item.name}
                  name={item.name as keyof PersonalFormData}
                >
                  {({ state, handleChange }) => {
                    const commonProps = {
                      name: item.name,
                      items: dropdownOptions[item.name as keyof typeof dropdownOptions],
                      selected: state.value?.toString(),
                      disabled: isLoading,
                      // eslint-disable-next-line sonarjs/no-nested-functions
                      onChange: (e: any) => handleChange(e.target.value), // NOSONAR
                    };
                    return (
                      <>
                        {item.type === 'radio-button-card-image' && (
                          <RadioButtonCard
                            {...commonProps}
                            type="card-image"
                          />
                        )}
                        {item.type === 'select' && (
                          <SelectBasic
                            {...commonProps}
                            title={t(item.title)}
                          />
                        )}
                      </>
                    );
                  }}
                </form.Field>
              );
            })}
          </FormAccordion>
        );
      })}
    </form>
  );
};

export default PersonalSettingForm;
