import { PageAlertStoreProvider } from '@/contexts/page-alert/page-alert.context';
import { mockGetHandlers } from '@/mocks/personal-settings.mock';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { PersonalSettingForm } from './PersonalSettingForm';
import { withPersonalSettingStoreProvider, withQueryProvider } from '.storybook/decorators';

/**
 * Personal Setting Form component for managing user's personal settings
 */
const meta = {
  title: 'Components/features/settings/personal-setting/PersonalSettingForm',
  component: PersonalSettingForm,
  parameters: { layout: 'centered' },
  decorators: [
    withPersonalSettingStoreProvider,
    withQueryProvider,
    (Story) => {
      return (
        <PageAlertStoreProvider>
          <Story />
        </PageAlertStoreProvider>
      );
    },
  ],
} satisfies Meta<typeof PersonalSettingForm>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default
 */
export const Default: Story = {
  parameters: {
    msw: { handlers: mockGetHandlers },
  },
  args: {
    setForm: () => {},
    setDirty: () => {},
    setLoading: () => {},
  },
};
