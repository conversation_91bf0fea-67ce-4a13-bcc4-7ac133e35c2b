/**
 * Configuration array defining the structure and form items for personal settings page.
 * Contains sections for appearance, language, preferences, and date/time settings.
 */
export const PERSONAL_FORM = [
  {
    title: 'common.label.appearance',
    description: 'settings.sentence.appearanceSectionDesc',
    defaultExpanded: true,
    formItems: [{ type: 'radio-button-card-image', name: 'appearance', title: '' }],
  },
  {
    title: 'common.label.language',
    description: 'settings.sentence.languageSectionDesc',
    defaultExpanded: true,
    formItems: [{ type: 'select', name: 'defaultLanguage', title: 'common.label.language' }],
  },
  {
    title: 'common.label.preferences',
    description: 'settings.sentence.preferencesSectionDesc',
    defaultExpanded: true,
    formItems: [{ type: 'select', name: 'recordPerPage', title: 'common.label.recordPerPage' }],
  },
  {
    title: 'common.label.dateAndTime',
    description: 'settings.sentence.dateAndTimeSectionDesc',
    defaultExpanded: true,
    formItems: [
      { type: 'select', name: 'defaultTimezone', title: 'common.label.timezone' },
      { type: 'select', name: 'dateFormat', title: 'common.label.dateFormat' },
      { type: 'select', name: 'timeFormat', title: 'common.label.timeFormat' },
    ],
  },
];
