import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { VisuallyHidden } from './VisuallyHidden';

/**
 * A hidden input component
 * <br>
 * Created just to pass test coverage
 */
const meta = {
  title: 'Components/base/inputs/VisuallyHidden/VisuallyHidden',
  component: VisuallyHidden,
  parameters: { layout: 'centered' },
} satisfies Meta<typeof VisuallyHidden>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default
 */
export const Default: Story = {};

/**
 * Radio
 */
export const Radio: Story = {
  args: { inputType: 'radio' },
};

/**
 * Input
 */
export const Input: Story = {
  args: { inputType: 'input' },
};

/**
 * Checkbox
 */
export const Checkbox: Story = {
  args: { inputType: 'checkbox' },
};
