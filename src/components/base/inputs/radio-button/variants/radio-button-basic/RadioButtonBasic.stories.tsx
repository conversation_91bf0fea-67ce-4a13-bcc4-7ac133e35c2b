import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useState } from 'react';
import { RadioButtonBasic } from './RadioButtonBasic';

/**
 * A basic radio button component
 */
const meta = {
  title: 'Components/base/inputs/Radio/RadioButtonBasic',
  component: RadioButtonBasic,
  parameters: { layout: 'centered' },
  argTypes: {
    name: { control: 'text', description: 'The name of the input field' },
    title: { control: 'text', description: 'The title of the input field' },
    display: { control: 'select', options: ['row', 'list'], description: 'The display method' },
    disabled: { control: 'boolean', description: 'If true, the component is disabled' },
  },
  decorators: [
    (Story, { args }) => {
      const [selected, setSelected] = useState('option1');

      return (
        <Story
          args={{
            ...args,
            selected: selected,
            onChange: (e: any) => setSelected(e.target.value),
          }}
        />
      );
    },
  ],
} satisfies Meta<typeof RadioButtonBasic>;

export default meta;

type Story = StoryObj<typeof meta>;

// Initialize items
const items = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
];

/**
 * Default
 */
export const Default: Story = {
  args: {
    name: 'radio-button',
    title: 'Radio button',
    description: 'This is the description of the radio button.',
    items: items,
    disabled: false,
  },
};

/**
 * Disabled
 */
export const Disabled: Story = {
  args: {
    name: 'radio-button',
    title: 'Disabled radio button',
    description: 'This is the description of the disabled radio button.',
    items: items,
    disabled: true,
  },
};

/**
 * List
 */
export const List: Story = {
  args: {
    name: 'radio-button',
    title: 'List radio button',
    description: 'This is the description of the list radio button.',
    display: 'list',
    items: items,
    disabled: true,
  },
};

/**
 * Without title or description
 */
export const Without: Story = {
  args: {
    name: 'radio-button',
    items: items,
    disabled: true,
  },
};
