import { Typography } from '@/components/base/typography';
import { Box, FormControlLabel, Radio, RadioGroup } from '@mui/material';
import type { RadioButtonBasicProps } from '../../RadioButton.type';

/**
 * RadioButtonBasic component provides a basic radio button group with optional
 * title and description, supporting both row and column layouts.
 */
export const RadioButtonBasic = ({
  name,
  title = '',
  description = '',
  selected = '',
  display = 'row',
  items = [],
  disabled = false,
  onChange,
}: RadioButtonBasicProps) => {
  const row = display === 'row';
  const showFormLabel = title || description;

  return (
    <>
      {showFormLabel && (
        <Box pb={1}>
          {title && <Typography variant="h5">{title}</Typography>}
          {description && (
            <Typography
              variant="subtitle2"
              color="text.secondary"
            >
              {description}
            </Typography>
          )}
        </Box>
      )}
      <RadioGroup
        row={row}
        aria-labelledby={`${name}-radio-button`}
        name={`${name}-radio-button`}
        value={selected}
        onChange={onChange}
      >
        {items.length > 0 &&
          items.map((item) => (
            <FormControlLabel
              key={item.value}
              value={item.value}
              control={<Radio />}
              label={item.label}
              disabled={disabled}
            />
          ))}
      </RadioGroup>
    </>
  );
};

export default RadioButtonBasic;
