import { VisuallyHidden } from '@/components/base/inputs/visually-hidden';
import { Typography } from '@/components/base/typography';
import {
  alpha,
  Avatar,
  Box,
  Card,
  Grid,
  ListItemAvatar,
  ListItemButton,
  ListItemText,
  Radio,
  Stack,
  useTheme,
  type RadioProps,
} from '@mui/material';
import { type ChangeEvent } from 'react';
import type { ItemList, RadioButtonCardProps } from '../../RadioButton.type';
import PlaceholderBox from './PlaceholderBox';

/**
 * RadioButtonCard component provides a radio button group with card-based layout
 * featuring customizable radio icon positioning and two display variants.
 *
 * Supports two main types:
 * - 'card': Traditional card layout with text content and optional radio buttons
 * - 'card-image': Card layout with image/content area and radio button controls
 *
 * Radio button icons can be positioned on the left, right, or hidden entirely
 * for a cleaner visual appearance while maintaining accessibility.
 */
export const RadioButtonCard = ({
  name,
  title = '',
  description = '',
  selected = '',
  radioIcon = 'left',
  type = 'card',
  items = [],
  disabled = false,
  onChange,
}: RadioButtonCardProps) => {
  const theme = useTheme();
  const showFormLabel = title || description;

  const handleClick = (value: string) => {
    // Create synthetic event to match MUI standard
    const syntheticEvent = {
      target: { value },
      currentTarget: { value },
    } as ChangeEvent<HTMLInputElement>;
    onChange?.(syntheticEvent);
  };

  const commonProps = (selected: string, item: ItemList): Partial<RadioProps> => ({
    checked: selected === item.value,
    onChange: onChange,
    value: item.value,
    name: `${name}-radio-buttons`,
    slotProps: { input: { 'aria-label': item.label } },
    color: 'primary',
    size: 'small',
    disabled: disabled,
  });

  return (
    <>
      {showFormLabel && (
        <Box pb={1}>
          {title && <Typography variant="h5">{title}</Typography>}
          {description && (
            <Typography
              variant="subtitle2"
              color="text.secondary"
            >
              {description}
            </Typography>
          )}
        </Box>
      )}
      <Grid
        container
        spacing={{ xs: 1, md: 2 }}
      >
        {items.map((item) => (
          <Grid
            key={item.label}
            spacing={{ xs: 12, md: 4 }}
          >
            <Card
              elevation={0}
              sx={{ border: 0 }}
            >
              <ListItemButton
                sx={{
                  px: type === 'card' ? 2 : '1px',
                  py: type === 'card' ? 1 : '1px',

                  ...(type === 'card' && { alignItems: 'flex-start' }),
                  ...(type !== 'card' && { flexDirection: 'column' }),

                  borderRadius: 'inherit',
                  boxShadow: `0 0 0 1px ${theme.palette.divider} inset`,

                  '&:hover': {
                    backgroundColor:
                      type === 'card' ? alpha(theme.palette.divider, 0.2) : 'background.paper',

                    ...(type !== 'card' && {
                      boxShadow: `0 0 0 1px ${theme.palette.primary.main} inset`,
                    }),
                  },

                  '&.Mui-selected': {
                    backgroundColor:
                      type === 'card'
                        ? alpha(theme.palette.primary.main, 0.05)
                        : 'background.paper',
                    boxShadow: `0 0 0 ${type === 'card' ? '2px' : '1px'} ${theme.palette.primary.main} inset`,

                    '&:hover': {
                      backgroundColor:
                        type === 'card'
                          ? alpha(theme.palette.primary.main, 0.05)
                          : 'background.paper',
                    },
                  },
                }}
                selected={selected === item.value}
                onClick={() => handleClick(item.value)}
                disabled={disabled}
                aria-label={item.label}
              >
                {type === 'card' && (
                  <>
                    {radioIcon === 'left' && (
                      <ListItemAvatar sx={{ minWidth: 28 }}>
                        <Radio
                          edge="start"
                          {...commonProps(selected, item)}
                        />
                      </ListItemAvatar>
                    )}
                    <ListItemText
                      disableTypography
                      primary={
                        <Typography
                          variant="h6"
                          noWrap
                          sx={{ pt: 0.2 }}
                          gutterBottom
                        >
                          {item.label}
                        </Typography>
                      }
                      secondary={
                        <Typography
                          color="text.secondary"
                          noWrap
                          sx={{
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            display: '-webkit-box',
                            whiteSpace: 'initial',
                          }}
                        >
                          {item.description}
                        </Typography>
                      }
                    />
                    {radioIcon === 'right' && (
                      <Radio
                        edge="end"
                        {...commonProps(selected, item)}
                      />
                    )}
                    {radioIcon === 'hidden' && (
                      <VisuallyHidden
                        inputType={'radio'}
                        {...commonProps(selected, item)}
                      />
                    )}
                  </>
                )}
                {type === 'card-image' && (
                  <>
                    <ListItemAvatar sx={{ width: '100%', px: '1px' }}>
                      <Avatar
                        variant="rounded"
                        sx={{
                          borderBottomRightRadius: 0,
                          borderBottomLeftRadius: 0,
                          backgroundColor: (theme) =>
                            theme.palette.mode === 'dark'
                              ? alpha(theme.palette.neutral[100], 0.06)
                              : 'neutral.100',
                          width: '100%',
                          minWidth: 200,
                          minHeight: 120,
                          p: 2,

                          '& div, img': {
                            width: '100%',
                            borderRadius: theme.shape.borderRadius + 'px',
                          },
                        }}
                      >
                        <PlaceholderBox flex={1}>{item.children}</PlaceholderBox>
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      sx={{ width: '100%' }}
                      disableTypography
                      primary={
                        <Stack
                          direction="row"
                          alignItems="center"
                          useFlexGap
                          width="100%"
                          px={2}
                          py={1}
                        >
                          {radioIcon === 'left' && (
                            <Radio
                              edge="start"
                              {...commonProps(selected, item)}
                            />
                          )}
                          {radioIcon === 'hidden' && (
                            <VisuallyHidden
                              inputType={'radio'}
                              {...commonProps(selected, item)}
                            />
                          )}
                          <Typography
                            variant="h6"
                            noWrap
                            sx={{ flex: 1 }}
                          >
                            {item.label}
                          </Typography>
                          {radioIcon === 'right' && (
                            <Radio
                              edge="end"
                              {...commonProps(selected, item)}
                            />
                          )}
                        </Stack>
                      }
                    />
                  </>
                )}
              </ListItemButton>
            </Card>
          </Grid>
        ))}
      </Grid>
    </>
  );
};

export default RadioButtonCard;
