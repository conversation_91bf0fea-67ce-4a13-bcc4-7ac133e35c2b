import Box from '@mui/material/Box';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { PlaceholderBox } from './PlaceholderBox';

/**
 * Placeholder box for radio button card image
 */
const meta = {
  title: 'Components/base/inputs/Radio/PlaceholderBox',
  component: PlaceholderBox,
  parameters: { layout: 'centered' },
  decorators: [
    (Story, { args }) => {
      return (
        <Box sx={{ width: 100, height: 100 }}>
          <Story args={{ ...args }} />
        </Box>
      );
    },
  ],
} satisfies Meta<typeof PlaceholderBox>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default
 */
export const Default: Story = {};

/**
 * Dark
 */
export const Dark: Story = {
  args: {
    dark: true,
  },
};

/**
 * Disable hover
 */
export const DisableHover: Story = {
  args: {
    dark: true,
    disableHover: true,
  },
};
