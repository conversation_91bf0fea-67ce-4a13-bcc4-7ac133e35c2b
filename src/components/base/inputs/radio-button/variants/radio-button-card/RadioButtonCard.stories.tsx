import { Typography } from '@/components/base/typography';
import Stack from '@mui/material/Stack';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useState } from 'react';
import { expect } from 'storybook/test';
import { RadioButtonCard } from './RadioButtonCard';

/**
 * A radio button component wrapped in a card layout
 */
const meta = {
  title: 'Components/base/inputs/Radio/RadioButtonCard',
  component: RadioButtonCard,
  parameters: { layout: 'centered' },
  argTypes: {
    name: { control: 'text', description: 'The name of the input field' },
    title: { control: 'text', description: 'The title of the input field' },
    radioIcon: {
      control: 'select',
      options: ['hidden', 'left', 'right'],
      description: 'The display method',
    },
    type: {
      control: 'select',
      options: ['card', 'card-image'],
      description: 'The type of radio button',
    },
    disabled: { control: 'boolean', description: 'If true, the component is disabled' },
  },
  decorators: [
    (Story, { args }) => {
      const [selected, setSelected] = useState('option1');

      return (
        <Story
          args={{
            ...args,
            selected: selected,
            onChange: (e: any) => setSelected(e.target.value),
          }}
        />
      );
    },
  ],
} satisfies Meta<typeof RadioButtonCard>;

export default meta;

type Story = StoryObj<typeof meta>;

// Initialize items
const items = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
];

/**
 * Default
 */
export const Default: Story = {
  args: {
    name: 'default-radio-button',
    title: 'Radio button',
    description: 'This is the description of the radio button.',
    items: items,
    disabled: false,
  },
  play: async ({ canvas, userEvent, step }) => {
    await step('Able to trigger onChange event on click', async () => {
      await userEvent.click(canvas.getByRole('button', { name: 'Option 2' }));
      expect(canvas.getByRole('button', { name: 'Option 2' })).toHaveClass('Mui-selected');
    });
  },
};

/**
 * Disabled
 */
export const Disabled: Story = {
  args: {
    name: 'disabled-radio-button',
    title: 'Disabled radio button',
    description: 'This is the description of the disabled radio button.',
    items: items,
    disabled: true,
  },
};

/**
 * Without title and description
 */
export const Without: Story = {
  args: {
    name: 'without-radio-button',
    items: items,
  },
};

/**
 * Radio icon
 */
export const RadioIcon: Story = {
  // This args is unnecessary
  // Declared just to suppress typescript error
  args: {
    items: [],
    name: '',
  },
  render: () => {
    const RadioIconComponent = () => {
      const name = 'radio-button';
      const [selected, setSelected] = useState('option1');
      return (
        <Stack spacing={3}>
          <Typography
            variant="body2"
            sx={{ mb: 2 }}
          >
            Radio button card with different radio icon position
          </Typography>
          <RadioButtonCard
            name={name}
            radioIcon="hidden"
            items={items}
            selected={selected}
            onChange={(e: any) => setSelected(e.target.value)}
          />
          <RadioButtonCard
            name={name}
            radioIcon="left"
            items={items}
            selected={selected}
            onChange={(e: any) => setSelected(e.target.value)}
          />
          <RadioButtonCard
            name={name}
            radioIcon="right"
            items={items}
            selected={selected}
            onChange={(e: any) => setSelected(e.target.value)}
          />
        </Stack>
      );
    };
    return <RadioIconComponent />;
  },
};

/**
 * Radio type
 */
export const RadioType: Story = {
  // This args is unnecessary
  // Declared just to suppress typescript error
  args: {
    items: [],
    name: '',
  },
  render: () => {
    const RadioTypeComponent = () => {
      const name = 'radio-button';
      const [selected, setSelected] = useState('option1');
      return (
        <Stack spacing={3}>
          <Typography
            variant="body2"
            sx={{ mb: 2 }}
          >
            Radio button card image with different radio icon position
          </Typography>
          <RadioButtonCard
            name={name}
            type="card-image"
            radioIcon="hidden"
            items={items}
            selected={selected}
            onChange={(e: any) => setSelected(e.target.value)}
          />
          <RadioButtonCard
            name={name}
            type="card-image"
            radioIcon="left"
            items={items}
            selected={selected}
            onChange={(e: any) => setSelected(e.target.value)}
          />
          <RadioButtonCard
            name={name}
            type="card-image"
            radioIcon="right"
            items={items}
            selected={selected}
            onChange={(e: any) => setSelected(e.target.value)}
          />
        </Stack>
      );
    };
    return <RadioTypeComponent />;
  },
};
