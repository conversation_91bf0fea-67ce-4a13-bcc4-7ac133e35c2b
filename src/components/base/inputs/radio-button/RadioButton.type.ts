import { type SxProps, type Theme } from '@mui/material';
import { type ReactNode } from 'react';

export interface ItemList {
  label: string;
  value: string;
  description?: string;
  children?: ReactNode;
}

interface SharedProps {
  name: string;
  title?: string;
  description?: string;
  selected?: string;
  disabled?: boolean;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export interface RadioButtonBasicProps extends SharedProps {
  display?: 'list' | 'row';
  items: ItemList[];
}

export interface RadioButtonCardProps extends SharedProps {
  radioIcon?: 'hidden' | 'left' | 'right';
  type?: 'card' | 'card-image';
  items: ItemList[];
}

export interface PlaceholderBoxProps {
  children?: ReactNode;
  height?: number;
  fixedHeight?: number;
  flex?: number;
  dark?: boolean;
  disableHover?: boolean;
  sx?: SxProps<Theme>;
}
