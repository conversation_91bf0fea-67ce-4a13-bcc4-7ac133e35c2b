import { type SelectChangeEvent } from '@mui/material/Select';

interface ItemList {
  label: string;
  value: string;
}

export interface SelectBasicProps {
  name: string;
  title?: string;
  description?: string;
  selected?: string;
  disabled?: boolean;
  onChange?: (event: SelectChangeEvent<string>, child: React.ReactNode) => void;
  items: ItemList[];
}

export type SelectProps = SelectBasicProps;
