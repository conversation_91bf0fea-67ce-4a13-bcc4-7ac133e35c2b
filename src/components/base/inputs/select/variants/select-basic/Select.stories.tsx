import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useState } from 'react';
import { SelectBasic } from './SelectBasic';

/**
 * A basic select component
 */
const meta = {
  title: 'Components/base/inputs/Select/SelectBasic',
  component: SelectBasic,
  parameters: { layout: 'centered' },
  argTypes: {
    name: { control: 'text', description: 'The name of the input field' },
    title: { control: 'text', description: 'The title of the input field' },
    description: { control: 'text', description: 'The description of the input field' },
    disabled: { control: 'boolean', description: 'If true, the component is disabled' },
  },
  decorators: [
    (Story, { args }) => {
      const [selected, setSelected] = useState();

      return (
        <Story
          args={{
            ...args,
            selected: selected,
            onChange: (e: any) => setSelected(e.target.value),
          }}
        />
      );
    },
  ],
} satisfies Meta<typeof SelectBasic>;

export default meta;

type Story = StoryObj<typeof meta>;

// Initialize items
const items = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
];

/**
 * Default
 */
export const Default: Story = {
  args: {
    name: 'select-input',
    title: 'Select input',
    description: 'This is the description of the select input.',
    items: items,
    disabled: false,
  },
};

/**
 * Disabled
 */
export const Disabled: Story = {
  args: {
    name: 'select-input',
    title: 'Disabled select input',
    description: 'This is the description of the disabled select input.',
    items: items,
    disabled: true,
  },
};
