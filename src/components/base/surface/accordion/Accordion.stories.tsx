import { Typography } from '@/components/base/typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { AccordionDetails, AccordionSummary, Stack } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import React, { useState } from 'react';
import { Accordion } from './Accordion';
import type { AccordionProps } from './Accordion.type';

const meta = {
  title: 'Components/base/surface/Accordion',
  component: Accordion,
  decorators: [(storyFn) => <Stack sx={{ width: 400 }}>{storyFn()}</Stack>],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    customStyle: { control: 'select', options: ['minimal', 'alternate', 'primary'] },
    defaultExpanded: { control: 'boolean' },
  },
} satisfies Meta<typeof Accordion>;

export default meta;

type Story = StoryObj<typeof meta>;

const AccordionStory = ({ customStyle, defaultExpanded }: AccordionProps) => {
  const [expanded, setExpanded] = useState<string | false>(defaultExpanded ? 'panel' : false);

  const handleChange = (panel: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <Accordion
      expanded={expanded === 'panel'}
      onChange={handleChange('panel')}
      customStyle={customStyle}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography>Accordion</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Typography>{`Content for the ${customStyle} styled accordion.`}</Typography>
      </AccordionDetails>
    </Accordion>
  );
};

export const Default: Story = {
  args: {
    defaultExpanded: true,
    children: <></>,
  },
  render: (args) => <AccordionStory {...args} />,
};

/**
 * Alternate Accordion with simplified styling
 */
export const Alternate: Story = {
  render: (args) => (
    <>
      <AccordionStory {...args} />
      <AccordionStory {...args} />
    </>
  ),
  args: {
    customStyle: 'alternate',
    defaultExpanded: true,
    children: <></>,
  },
};

/**
 * Minimal Accordion with simplified styling
 */
export const Minimal: Story = {
  render: (args) => (
    <>
      <AccordionStory {...args} />
      <AccordionStory {...args} />
    </>
  ),
  args: {
    customStyle: 'minimal',
    defaultExpanded: false,
    children: <></>,
  },
};

/**
 * Plus Accordion with simplified styling
 */
export const Plus: Story = {
  render: (args) => (
    <>
      <AccordionStory {...args} />
      <AccordionStory {...args} />
    </>
  ),
  args: {
    customStyle: 'plus',
    defaultExpanded: false,
    children: <></>,
  },
};

/**
 * Primary Accordion with simplified styling
 */
export const Primary: Story = {
  render: (args) => (
    <>
      <AccordionStory {...args} />
      <AccordionStory {...args} />
    </>
  ),
  args: {
    customStyle: 'primary',
    defaultExpanded: true,
    children: <></>,
  },
};

/**
 * Form Accordion with simplified styling
 */
export const Form: Story = {
  render: (args) => (
    <>
      <AccordionStory {...args} />
      <br />
      <AccordionStory {...args} />
    </>
  ),
  args: {
    customStyle: 'form',
    defaultExpanded: false,
    children: <></>,
  },
};

/**
 * Multiple accordions demonstrating expand/collapse behavior
 */
export const MultipleAccordions: Story = {
  args: {
    children: 'Multiple Accordions Example',
  },
  parameters: {
    controls: { disable: true },
  },
  render: () => (
    <>
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>First Accordion</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>Content for the first accordion.</Typography>
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Second Accordion</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>Content for the second accordion.</Typography>
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Third Accordion</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>Content for the third accordion.</Typography>
        </AccordionDetails>
      </Accordion>
    </>
  ),
};
