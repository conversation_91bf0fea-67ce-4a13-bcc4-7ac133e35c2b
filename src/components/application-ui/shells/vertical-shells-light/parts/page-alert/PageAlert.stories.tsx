import {
  PageAlertStoreProvider,
  usePageAlertStore,
} from '@/contexts/page-alert/page-alert.context';
import { SIDEBAR_WIDTH } from '@/theme/utils';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useEffect } from 'react';
import { PageAlert } from './PageAlert';

/**
 * Personal Setting Form component for managing user's personal settings
 */
const meta = {
  title: 'Components/application-ui/shells/vertical-shells-light/parts/PageAlert',
  component: PageAlert,
  parameters: { layout: 'centered' },
  argTypes: {
    placement: {
      control: 'select',
      options: ['top', 'bottom'],
      description: 'The placement of the page alert',
    },
  },
  decorators: [
    (Story) => (
      <PageAlertStoreProvider>
        <Stack
          direction="row"
          sx={{ border: '1px solid #ccc', padding: 2 }}
        >
          <Box sx={{ width: `${SIDEBAR_WIDTH}px` }}>Sidebar</Box>
          <Box sx={{ width: 500, height: 200 }}>
            <Story />
          </Box>
        </Stack>
      </PageAlertStoreProvider>
    ),
  ],
} satisfies Meta<typeof PageAlert>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Top
 */
export const Default: Story = {
  args: {
    placement: 'top',
  },
  render: (args) => {
    const AlertWrapper = () => {
      const { showAlert } = usePageAlertStore((state) => state);

      useEffect(() => {
        showAlert({ title: 'Success', message: 'Alert at the top', severity: 'success' });
      }, [showAlert]);

      return (
        <PageAlert {...args}>
          <div>Page content</div>
        </PageAlert>
      );
    };

    return <AlertWrapper />;
  },
};

/**
 * Bottom
 * <br />
 * Note:
 * The alert is showing outside the container in this storybook only.
 * In actual page, it will be within container.
 */
export const Bottom: Story = {
  args: {
    placement: 'bottom',
  },
  render: (args) => {
    const AlertWrapper = () => {
      const { showAlert } = usePageAlertStore((state) => state);

      useEffect(() => {
        showAlert({ title: 'Error', message: 'Alert at the bottom', severity: 'error' });
      }, [showAlert]);

      return (
        <PageAlert {...args}>
          <div>Page content</div>
        </PageAlert>
      );
    };

    return <AlertWrapper />;
  },
};
