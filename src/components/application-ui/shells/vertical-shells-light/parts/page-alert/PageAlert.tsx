import { FormAlert } from '@/components/base/feedback/alert';
import { usePageAlertStore } from '@/contexts/page-alert/page-alert.context';
// import useScrollDirection from '@/hooks/ui/use-scroll-direction.hook';\
import {
  // HEADER_HEIGHT,
  SIDEBAR_WIDTH,
} from '@/theme/utils';
import Box from '@mui/material/Box';
import { type PageAlertProps } from './PageAlert.type';

/**
 * PageAlert component displays alert messages at the top or bottom of the page
 * with configurable placement and automatic state management.
 */
export const PageAlert = ({ placement, children }: PageAlertProps) => {
  const { title, message, severity, display, closeAlert } = usePageAlertStore((state) => state);
  // const scroll = useScrollDirection();\

  return (
    <>
      {display === true && placement === 'top' && (
        // Uncomment if need to be fixed to top
        // <Box
        //   sx={{
        //     position: 'fixed',
        //     top: scroll === 'down' ? HEADER_HEIGHT : HEADER_HEIGHT * 1.5,
        //     transition: theme.transitions.create(['height']),
        //     left: { xs: 0, lg: `${SIDEBAR_WIDTH}px` },
        //     right: 0,
        //     zIndex: 9999,
        //     borderRadius: 0,
        //     margin: 0,
        //   }}
        // >
        <FormAlert
          severity={severity}
          title={title}
          message={message}
          onClose={closeAlert}
        />
        // </Box>
      )}
      {children}
      {display === true && placement === 'bottom' && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 0,
            left: { xs: 0, lg: `${SIDEBAR_WIDTH}px` },
            right: 0,
            zIndex: 9999,
            borderRadius: 0,
            margin: 0,
          }}
        >
          <FormAlert
            severity={severity}
            title={title}
            message={message}
            onClose={closeAlert}
          />
        </Box>
      )}
    </>
  );
};

export default PageAlert;
