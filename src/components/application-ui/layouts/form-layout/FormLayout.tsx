import { UnsavedChangesDialog } from '@/components/application-ui/alert-dialog';
import { FormHeading } from '@/components/application-ui/form-section/form-heading';
import { useCustomization } from '@/hooks/ui/use-customization.hook';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Divider from '@mui/material/Divider';
import { type FormLayoutProps } from './FormLayout.type';

export const FormLayout = ({
  children,
  title,
  description,
  submit,
  reset,
  unsaved,
}: FormLayoutProps) => {
  const { stretch } = useCustomization();

  return (
    <>
      <FormHeading
        title={title}
        description={description}
        submit={submit}
        reset={reset}
      />
      <Divider />
      <Box py={{ xs: 2, sm: 3 }}>
        <Container maxWidth={stretch ? false : 'xl'}>
          {children}
          {unsaved && <UnsavedChangesDialog enabled={unsaved.enabled} />}
        </Container>
      </Box>
    </>
  );
};

export default FormLayout;
