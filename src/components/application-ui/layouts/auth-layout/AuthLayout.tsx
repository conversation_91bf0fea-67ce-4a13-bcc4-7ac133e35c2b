import { Box, Grid, Paper } from '@mui/material';
import type { JSX } from 'react';
import type { AuthLayoutProps } from './AuthLayout.type';

export function AuthLayout({ children }: Readonly<AuthLayoutProps>): JSX.Element {
  return (
    <Grid
      container
      component="main" // real <main> for a11y
      minHeight="100vh" // reliable full-height
      width="100%"
    >
      <Grid
        component={Paper}
        elevation={6}
        square
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%', // ensure Paper spans width
        }}
        p={1}
      >
        <Box
          border={1}
          borderColor="divider"
          pt={{ xs: 2, sm: 8 }}
          pb={{ xs: 2, sm: 8 }}
          px={{ xs: 2, sm: 10 }}
        >
          {children}
        </Box>
      </Grid>
    </Grid>
  );
}

export default AuthLayout;
