import { AlertDialog } from '@/components/base/feedback/dialog';
import { Button } from '@/components/base/inputs/button';
import { useDialog } from '@/hooks/ui/use-dialog.hook';
import { useEffect } from 'react';
import { type ConfirmationDialogProps } from './ConfirmationDialog.type';

/**
 * A confirmation dialog component with customizable title, description, and action buttons.
 */
export const ConfirmationDialog = ({
  display,
  title,
  description,
  cancelButton,
  cancelAction,
  confirmButton,
  confirmAction,
}: ConfirmationDialogProps) => {
  const { open, handleOpen, handleClose } = useDialog();

  const onClose = () => {
    handleClose();
    cancelAction();
  };

  const onConfirm = () => {
    handleClose();
    confirmAction?.();
  };

  useEffect(() => {
    if (display) handleOpen();
  }, [display, handleOpen]);

  return (
    <AlertDialog
      open={open}
      onClose={onClose}
      title={title}
      message={description}
      actions={
        <>
          {cancelButton && (
            <Button
              variant="text"
              color={cancelButton.color}
              onClick={onClose}
            >
              {cancelButton.label}
            </Button>
          )}
          {confirmButton && (
            <Button
              data-nprogress="true"
              variant="text"
              color={confirmButton.color}
              onClick={onConfirm}
              autoFocus
            >
              {confirmButton.label}
            </Button>
          )}
        </>
      }
    />
  );
};

export default ConfirmationDialog;
