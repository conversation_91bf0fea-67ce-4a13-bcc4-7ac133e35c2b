import { But<PERSON> } from '@/components/base/inputs/button';
import { type PaletteColorKey } from '@/theme/colors';
import Box from '@mui/material/Box';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useState } from 'react';
import { ConfirmationDialog } from './ConfirmationDialog';

/**
 * A confirmation alert dialog component
 */
const meta = {
  title: 'Components/application-ui/alert-dialog/ConfirmationDialog',
  component: ConfirmationDialog,
  parameters: { layout: 'centered' },
  decorators: [
    (Story, { args }) => {
      const [display, setDisplay] = useState(false);
      const handleClose = () => setDisplay(false);

      return (
        <Box sx={{ minWidth: 400, height: 300, position: 'relative', textAlign: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              setDisplay(true);
              setTimeout(() => setDisplay(false), 1000);
            }}
          >
            Click
          </Button>
          <Story
            args={{
              ...args,
              display: display,
              cancelAction: handleClose,
              confirmAction: handleClose,
            }}
          />
        </Box>
      );
    },
  ],
} satisfies Meta<typeof ConfirmationDialog>;

export default meta;

type Story = StoryObj<typeof meta>;

const cancelButton = { label: 'Cancel', color: 'secondary' as PaletteColorKey };
const confirmButton = { label: 'Confirm', color: 'primary' as PaletteColorKey };

/**
 * Default
 */
export const Default: Story = {
  args: {
    display: true,
    title: 'Default',
    description: 'A default confirmation dialog.',
    cancelAction: () => {},
    cancelButton,
    confirmButton,
  },
};

/**
 * No description
 */
export const NoDescription: Story = {
  args: {
    display: true,
    title: 'No description',
    cancelAction: () => {},
    cancelButton,
    confirmButton,
  },
};

/**
 * No cancel button
 */
export const NoCancelButton: Story = {
  args: {
    display: true,
    title: 'No cancel button',
    description: 'A confirmation dialog without cancel button.',
    cancelAction: () => {},
    confirmButton,
  },
};

/**
 * No confirm button
 */
export const NoConfirmButton: Story = {
  args: {
    display: true,
    title: 'No confirm button',
    description: 'A confirmation dialog without confirm button.',
    cancelAction: () => {},
    cancelButton,
  },
};

/**
 * No button
 */
export const NoButton: Story = {
  args: {
    display: true,
    title: 'No button',
    description: 'A confirmation dialog without any buttons.',
    cancelAction: () => {},
  },
};

/**
 * Customisable button
 */
export const CustomisableButton: Story = {
  args: {
    display: true,
    title: 'Customisable button',
    description: 'A confirmation dialog with customisable buttons.',
    cancelAction: () => {},
    cancelButton: {
      label: 'hi',
      color: 'livingCoral',
    },
    confirmButton: {
      label: 'hello',
      color: 'darkViolet',
    },
  },
};
