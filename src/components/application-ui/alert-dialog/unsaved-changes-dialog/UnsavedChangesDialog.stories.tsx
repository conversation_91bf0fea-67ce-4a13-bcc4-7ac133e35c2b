import { But<PERSON> } from '@/components/base/inputs/button';
import Box from '@mui/material/Box';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useState } from 'react';
import { UnsavedChangesDialog } from './UnsavedChangesDialog';
import { withNavigationGuardProvider } from '.storybook/decorators';

/**
 * A alert dialog component for unsaved changes
 */
const meta = {
  title: 'Components/application-ui/alert-dialog/UnsavedChangesDialog',
  component: UnsavedChangesDialog,
  parameters: { layout: 'centered' },
  decorators: [
    withNavigationGuardProvider,
    (Story, { args }) => {
      const [forceDisplay, setForceDisplay] = useState(false);

      return (
        <Box sx={{ minWidth: 400, height: 300, position: 'relative', textAlign: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => setForceDisplay(true)}
          >
            Click
          </Button>
          <Story
            args={{
              ...args,
              storybookMock: {
                forceDisplay: forceDisplay,
                cancel: () => setForceDisplay(false),
                confirm: () => setForceDisplay(false),
              },
            }}
          />
        </Box>
      );
    },
  ],
} satisfies Meta<typeof UnsavedChangesDialog>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default
 */
export const Default: Story = {
  args: { enabled: true },
};
