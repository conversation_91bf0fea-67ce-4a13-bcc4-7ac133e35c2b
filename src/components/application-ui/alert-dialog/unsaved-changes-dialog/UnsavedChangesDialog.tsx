import { ConfirmationDialog } from '@/components/application-ui/alert-dialog/confirmation-dialog';
import { transformText } from '@/utils/text-transform.util';
import { useNavigationGuard } from 'next-navigation-guard';
import { useTranslation } from 'react-i18next';
import { type UnsavedChangesDialogProps } from './UnsavedChangesDialog.type';

/**
 * A dialog component that warns users about unsaved changes when they attempt to navigate
 * away from a page.
 */
export const UnsavedChangesDialog = ({ enabled, storybookMock }: UnsavedChangesDialogProps) => {
  const { t } = useTranslation();
  const navGuard = useNavigationGuard({ enabled: enabled });

  const actions = {
    cancel: navGuard.reject,
    confirm: navGuard.accept,
  };

  let display = navGuard.active;
  // To force display in storybook
  if (storybookMock) {
    display = storybookMock.forceDisplay;
    actions.cancel = storybookMock.cancel;
    actions.confirm = storybookMock.confirm;
  }

  return (
    <ConfirmationDialog
      display={display}
      title={transformText(t('common.sentence.unsavedTitle'), 'sentenceCase')}
      description={t('common.sentence.unsavedDesc')}
      cancelButton={{ label: t('common.label.cancel'), color: 'secondary' }}
      cancelAction={actions.cancel}
      confirmButton={{ label: t('common.label.confirm'), color: 'warning' }}
      confirmAction={actions.confirm}
    />
  );
};

export default UnsavedChangesDialog;
