import { useDialog } from '@/hooks/ui/use-dialog.hook';
import { fireEvent, render, screen } from '@testing-library/react';
import { useNavigationGuard } from 'next-navigation-guard';
import { beforeEach, describe, expect, it, vi, type Mock } from 'vitest';
import { UnsavedChangesDialog } from './UnsavedChangesDialog';
import { type UnsavedChangesDialogProps } from './UnsavedChangesDialog.type';

vi.mock('next-navigation-guard');
vi.mock('@/hooks/ui/use-dialog.hook');

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'common.label.cancel': 'Cancel',
        'common.label.confirm': 'Confirm',
      };
      return translations[key] ?? key;
    },
  }),
}));

describe('UnsavedChangesDialog', () => {
  const renderComponent = () => {
    const defaultProps: UnsavedChangesDialogProps = {
      enabled: true,
      storybookMock: { forceDisplay: true, cancel: vi.fn(), confirm: vi.fn() },
    };
    return render(<UnsavedChangesDialog {...defaultProps} />);
  };

  const mockUseNavigationGuard = useNavigationGuard as Mock;
  const mockNavGuard = {
    active: false,
    accept: vi.fn(),
    reject: vi.fn(),
  };

  const mockUseDialog = useDialog as Mock;
  const mockDialog = {
    open: true,
    handleOpen: vi.fn(),
    handleClose: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    mockUseNavigationGuard.mockReturnValue(mockNavGuard);
    mockUseDialog.mockReturnValue(mockDialog);
  });

  it('should handle cancel button click correctly', () => {
    renderComponent();

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockDialog.handleClose).toHaveBeenCalled();
  });

  it('should handle confirm button click correctly', () => {
    renderComponent();

    const confirmButton = screen.getByText('Confirm');
    fireEvent.click(confirmButton);

    expect(mockDialog.handleClose).toHaveBeenCalled();
  });
});
