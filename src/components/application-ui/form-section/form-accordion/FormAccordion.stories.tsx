import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { FormAccordion } from './FormAccordion';

/**
 * A form wrapper accordion as form section
 */
const meta = {
  title: 'Components/application-ui/form-section/form-accordion/FormAccordion',
  component: FormAccordion,
  parameters: { layout: 'centered' },
  argTypes: {
    title: { control: 'text', description: 'The title of the form section' },
    description: { control: 'text', description: 'The description of the form section' },
    defaultExpanded: { control: 'boolean', description: 'Default expand or collapse' },
  },
} satisfies Meta<typeof FormAccordion>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default
 */
export const Default: Story = {
  args: {
    title: 'form section',
    children: 'Sample content',
  },
};

/**
 * With description
 */
export const Description: Story = {
  args: {
    title: 'description form section',
    description: 'this is the description of the form section.',
    children: 'Sample content',
  },
};

/**
 * Expanded
 */
export const Expanded: Story = {
  args: {
    title: 'expanded form section',
    description: 'this is default expanded form section.',
    defaultExpanded: true,
    children: 'Sample content',
  },
};
