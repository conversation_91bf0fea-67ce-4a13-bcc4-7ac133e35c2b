import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Stack from '@mui/material/Stack';
import type { FormAccordionProps } from './FormAccordion.type';

/**
 * FormAccordion component provides a collapsible accordion interface for organizing
 * form content into logical sections with title and optional description.
 */
export const FormAccordion = ({
  children,
  defaultExpanded = false,
  title,
  description = '',
  ...otherProps
}: FormAccordionProps) => {
  return (
    <Accordion
      customStyle="form"
      defaultExpanded={defaultExpanded}
      sx={{ mb: 2, borderRadius: 1, overflow: 'hidden' }}
      {...otherProps}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Stack>
          <Typography
            variant="h6"
            caseTransform="sentenceCase"
          >
            {title}
          </Typography>
          <Typography
            variant="subtitle2"
            caseTransform="sentenceCase"
            color="text.secondary"
          >
            {description}
          </Typography>
        </Stack>
      </AccordionSummary>
      <AccordionDetails>{children}</AccordionDetails>
    </Accordion>
  );
};

export default FormAccordion;
