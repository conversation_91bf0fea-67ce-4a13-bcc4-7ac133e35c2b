import { PageHeading } from '@/components/base/headings/page-heading';
import Stack from '@mui/material/Stack';
import { ResetButton, SubmitButton } from './buttons';
import { type FormHeadingProps } from './FormHeading.type';

export const FormHeading = ({ title, description, submit, reset }: FormHeadingProps) => {
  return (
    <PageHeading
      title={title}
      description={description}
      actions={
        <Stack
          direction={{ xs: 'row' }}
          spacing={1.5}
          whiteSpace="nowrap"
          alignItems="center"
          width={{ xs: '100%', sm: '100%', md: 'auto' }}
        >
          {reset && <ResetButton {...reset} />}
          {submit && <SubmitButton {...submit} />}
        </Stack>
      }
    />
  );
};

export default FormHeading;
