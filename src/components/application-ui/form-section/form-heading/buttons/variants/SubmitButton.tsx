import { Button } from '@/components/base/inputs/button';
import { type SubmitButtonProps } from '../Buttons.type';

export const SubmitButton = ({
  label,
  disabled = false,
  loading = false,
  onClick,
}: SubmitButtonProps) => {
  return (
    <Button
      variant="contained"
      loading={loading}
      fullWidth
      disabled={disabled}
      onClick={onClick}
    >
      {label}
    </Button>
  );
};

export default SubmitButton;
