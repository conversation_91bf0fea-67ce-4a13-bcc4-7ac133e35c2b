import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { ResetButton } from './ResetButton';

/**
 * A reset button in form heading component
 */
const meta = {
  title: 'Components/application-ui/form-section/form-heading/buttons/ResetButton',
  component: ResetButton,
  parameters: { layout: 'centered' },
} satisfies Meta<typeof ResetButton>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default
 */
export const Default: Story = {
  args: { label: 'Reset' },
};

/**
 * Disabled state
 */
export const Disabled: Story = {
  args: { label: 'Disabled', disabled: true },
};
