import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { SubmitButton } from './SubmitButton';

/**
 * A submit button in form heading component
 */
const meta = {
  title: 'Components/application-ui/form-section/form-heading/buttons/SubmitButton',
  component: SubmitButton,
  parameters: { layout: 'centered' },
} satisfies Meta<typeof SubmitButton>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default
 */
export const Default: Story = {
  args: { label: 'Save' },
};

/**
 * Loading state
 */
export const Loading: Story = {
  args: { label: 'Loading', loading: true },
};

/**
 * Disabled state
 */
export const Disabled: Story = {
  args: { label: 'Disabled', disabled: true },
};
