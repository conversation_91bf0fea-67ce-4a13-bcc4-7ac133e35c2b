import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { FormHeading } from './FormHeading';

/**
 * A form heading component
 */
const meta = {
  title: 'Components/application-ui/form-section/form-heading/FormHeading',
  component: FormHeading,
  parameters: { layout: 'fullscreen' },
} satisfies Meta<typeof FormHeading>;

export default meta;

type Story = StoryObj<typeof meta>;

// Init
const submit = { label: 'Submit', loading: false, disabled: false, onClick: () => {} };
const reset = { label: 'Reset', disabled: false, onClick: () => {} };

/**
 * Default
 */
export const Default: Story = {
  args: {
    title: 'Default',
    description: 'Heading with all props set.',
    submit: submit,
    reset: reset,
  },
};

/*
 * No description
 */
export const NoDescription: Story = {
  args: {
    title: 'No description',
    description: '',
    submit: submit,
    reset: reset,
  },
};

/*
 * Loading state
 */
export const Loading: Story = {
  args: {
    title: 'Loading',
    description: 'Submit button is loading.',
    submit: { ...submit, loading: true },
    reset: reset,
  },
};

/*
 * Disabled state
 */
export const Disabled: Story = {
  args: {
    title: 'Disabled',
    description: 'Action buttons are disabled.',
    submit: { ...submit, disabled: true },
    reset: { ...reset, disabled: true },
  },
};

/*
 * No actions
 */
export const NoActions: Story = {
  args: {
    title: 'No actions',
    description: 'No action buttons are shown.',
  },
};
