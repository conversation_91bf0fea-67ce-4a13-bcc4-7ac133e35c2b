'use client';

import {
  createPersonalSettingStore,
  type PersonalSettingStore,
} from '@/store/setting/personal-setting.store';
import { createContext, useContext, useRef, type ReactNode } from 'react';
import { useStore } from 'zustand';

/**
 * Type definition for the personal setting store API
 * Represents the return type of createPersonalSettingStore function
 */
export type PersonalSettingStoreApi = ReturnType<typeof createPersonalSettingStore>;

/**
 * React context for sharing personal setting store across components
 * Provides access to the Zustand store instance throughout the component tree
 */
export const PersonalSettingStoreContext = createContext<PersonalSettingStoreApi | undefined>(
  undefined
);

/**
 * Props interface for PersonalSettingStoreProvider component
 */
export interface PersonalSettingStoreProviderProps {
  children: ReactNode;
}

/**
 * Provider component that makes personal setting store available to child components
 *
 * Creates a single store instance per provider and shares it via React Context.
 * The store instance is created lazily and persists for the lifetime of the provider.
 */
export const PersonalSettingStoreProvider = ({ children }: PersonalSettingStoreProviderProps) => {
  const storeRef = useRef<PersonalSettingStoreApi | null>(null);
  storeRef.current ??= createPersonalSettingStore();

  return (
    <PersonalSettingStoreContext.Provider value={storeRef.current}>
      {children}
    </PersonalSettingStoreContext.Provider>
  );
};

/**
 * Custom hook to access personal setting store with selector pattern
 *
 * Provides type-safe access to the personal setting store state and actions.
 * Uses Zustand's selector pattern for optimal re-rendering performance.
 *
 * @example
 * ```typescript
 * // Select specific state
 * const appearance = usePersonalSettingStore((state) => state.appearance);
 *
 * // Select multiple values
 * const { version, update } = usePersonalSettingStore((state) => ({
 *   version: state.version,
 *   update: state.update
 * }));
 *
 * // Select entire state
 * const personalSettings = usePersonalSettingStore((state) => state);
 * ```
 */
export const usePersonalSettingStore = <T,>(selector: (store: PersonalSettingStore) => T): T => {
  const personalSettingStoreContext = useContext(PersonalSettingStoreContext);

  if (!personalSettingStoreContext) {
    throw new Error(`usePersonalSettingStore must be used within PersonalSettingStoreProvider`);
  }

  return useStore(personalSettingStoreContext, selector);
};
