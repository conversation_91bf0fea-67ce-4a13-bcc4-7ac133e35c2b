import { act, render, renderHook, screen } from '@testing-library/react';
import type { ReactNode } from 'react';
import { describe, expect, it, vi } from 'vitest';
import {
  PersonalSettingStoreProvider,
  usePersonalSettingStore,
  type PersonalSettingStoreProviderProps,
} from './personal-setting.context';
import '@testing-library/jest-dom';

// Test component that uses the hook
const TestComponent = ({ selector }: { selector?: (state: any) => any }) => {
  const data = usePersonalSettingStore(selector || ((state) => state));
  return <div data-testid="test-component">{JSON.stringify(data)}</div>;
};

// Wrapper component for testing
const createWrapper = (props?: Partial<PersonalSettingStoreProviderProps>) => {
  return ({ children }: { children: ReactNode }) => (
    <PersonalSettingStoreProvider {...props}>{children}</PersonalSettingStoreProvider>
  );
};

describe('PersonalSettingStoreProvider', () => {
  it('should render children without errors', () => {
    render(
      <PersonalSettingStoreProvider>
        <div data-testid="child">Test Child</div>
      </PersonalSettingStoreProvider>
    );

    expect(screen.getByTestId('child')).toBeInTheDocument();
    expect(screen.getByText('Test Child')).toBeInTheDocument();
  });

  it('should provide store context to children', () => {
    render(
      <PersonalSettingStoreProvider>
        <TestComponent />
      </PersonalSettingStoreProvider>
    );

    const component = screen.getByTestId('test-component');
    expect(component).toBeInTheDocument();

    // Should contain the initial store state
    const content = component.textContent;
    expect(content).toContain('"version":0');
    expect(content).toContain('"appearance":""');
  });

  it('should create separate store instances for different providers', () => {
    render(
      <div>
        <PersonalSettingStoreProvider>
          <div data-testid="store1">
            <TestComponent selector={(state) => state.version} />
          </div>
        </PersonalSettingStoreProvider>
        <PersonalSettingStoreProvider>
          <div data-testid="store2">
            <TestComponent selector={(state) => state.version} />
          </div>
        </PersonalSettingStoreProvider>
      </div>
    );

    const components = screen.getAllByTestId('test-component');
    expect(components).toHaveLength(2);

    // Both should have initial version 0
    components.forEach((component) => {
      expect(component.textContent).toBe('0');
    });
  });

  it('should maintain state across multiple components', () => {
    const Component1 = () => {
      const appearance = usePersonalSettingStore((state) => state.appearance);
      const update = usePersonalSettingStore((state) => state.update);

      return (
        <div>
          <span data-testid="appearance-1">{appearance}</span>
          <button
            data-testid="update-1"
            onClick={() => update({ appearance: 'dark' })}
          >
            Update
          </button>
        </div>
      );
    };

    const Component2 = () => {
      const appearance = usePersonalSettingStore((state) => state.appearance);
      return <span data-testid="appearance-2">{appearance}</span>;
    };

    render(
      <PersonalSettingStoreProvider>
        <Component1 />
        <Component2 />
      </PersonalSettingStoreProvider>
    );

    // Both components should show initial state
    expect(screen.getByTestId('appearance-1')).toHaveTextContent('');
    expect(screen.getByTestId('appearance-2')).toHaveTextContent('');

    // Update from component 1
    act(() => {
      screen.getByTestId('update-1').click();
    });

    // Both components should reflect the update
    expect(screen.getByTestId('appearance-1')).toHaveTextContent('dark');
    expect(screen.getByTestId('appearance-2')).toHaveTextContent('dark');
  });

  it('should isolate state between different provider instances', () => {
    const UpdateComponent = ({ testId }: { testId: string }) => {
      const appearance = usePersonalSettingStore((state) => state.appearance);
      const update = usePersonalSettingStore((state) => state.update);

      return (
        <div>
          <span data-testid={`appearance-${testId}`}>{appearance}</span>
          <button
            data-testid={`update-${testId}`}
            onClick={() => update({ appearance: `updated-${testId}` })}
          >
            Update
          </button>
        </div>
      );
    };

    render(
      <div>
        <PersonalSettingStoreProvider>
          <UpdateComponent testId="1" />
        </PersonalSettingStoreProvider>
        <PersonalSettingStoreProvider>
          <UpdateComponent testId="2" />
        </PersonalSettingStoreProvider>
      </div>
    );

    // Both should start with default state
    expect(screen.getByTestId('appearance-1')).toHaveTextContent('');
    expect(screen.getByTestId('appearance-2')).toHaveTextContent('');

    // Update from component 1
    act(() => {
      screen.getByTestId('update-1').click();
    });

    // Only store from component 1 is updated
    expect(screen.getByTestId('appearance-1')).toHaveTextContent('updated-1');
    expect(screen.getByTestId('appearance-2')).toHaveTextContent('');

    // Update from component 2
    act(() => {
      screen.getByTestId('update-2').click();
    });

    // Both stores should have their own values
    expect(screen.getByTestId('appearance-1')).toHaveTextContent('updated-1');
    expect(screen.getByTestId('appearance-2')).toHaveTextContent('updated-2');
  });
});

describe('usePersonalSettingStore', () => {
  it('should return selected state from store', () => {
    const { result } = renderHook(() => usePersonalSettingStore((state) => state.version), {
      wrapper: createWrapper(),
    });

    expect(result.current).toBe(0); // default value
  });

  it('should return multiple selected values', () => {
    const { result } = renderHook(
      () => {
        const version = usePersonalSettingStore((state) => state.version);
        const appearance = usePersonalSettingStore((state) => state.appearance);
        const update = usePersonalSettingStore((state) => state.update);

        return { version, appearance, update };
      },
      { wrapper: createWrapper() }
    );

    expect(result.current.version).toBe(0);
    expect(result.current.appearance).toBe('');
    expect(typeof result.current.update).toBe('function');
  });

  it('should return entire state when no selector provided', () => {
    const { result } = renderHook(() => usePersonalSettingStore((state) => state), {
      wrapper: createWrapper(),
    });

    const data = {
      version: 0,
      appearance: '',
      recordPerPage: '',
      dateFormat: '',
      timeFormat: '',
      defaultLanguage: '',
      defaultTimezone: '',
    };

    expect(result.current).toMatchObject(data);
    expect(typeof result.current.update).toBe('function');
  });

  it('should update state when update action is called', () => {
    const { result } = renderHook(
      () => {
        const appearance = usePersonalSettingStore((state) => state.appearance);
        const update = usePersonalSettingStore((state) => state.update);
        return { appearance, update };
      },
      { wrapper: createWrapper() }
    );

    expect(result.current.appearance).toBe('');

    act(() => {
      result.current.update({ appearance: 'dark' });
    });

    expect(result.current.appearance).toBe('dark');
  });

  it('should throw error when used outside provider', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      // eslint-disable-next-line sonarjs/no-nested-functions
      renderHook(() => usePersonalSettingStore((state) => state)); // NOSONAR
    }).toThrow('usePersonalSettingStore must be used within PersonalSettingStoreProvider');

    consoleSpy.mockRestore();
  });
});
