import { useRefMounted } from '@/hooks/lifecycle';
import { authService } from '@/services/auth/auth.service';
import { isSuccessResponse } from '@/types/api-responses.type';
import { createContext, useCallback, useEffect, useMemo, useState, type JSX } from 'react';
import { INITIAL_AUTH_STATE } from './auth.constant';
import type { AuthContextValue, AuthProviderProps, AuthState } from './auth.type';

/**
 * Context for authentication state and functions
 *
 * This context provides access to the current authentication state and
 * functions to manage authentication throughout the application.
 */
export const AuthContext = createContext<AuthContextValue | undefined>(undefined);

/**
 * Name for the context, useful for debugging
 */
AuthContext.displayName = 'AuthContext';

export const AuthConsumer = AuthContext.Consumer;

/**
 * Provider component for authentication context
 *
 * This component initializes and manages the authentication state, providing
 * authentication-related data and functions to its children components.
 */
export const AuthProvider = ({ children }: Readonly<AuthProviderProps>): JSX.Element => {
  // State for authentication data
  const [state, setState] = useState<AuthState>(INITIAL_AUTH_STATE);
  const isMounted = useRefMounted();

  const safeSetState = useCallback(
    (updater: (prev: AuthState) => AuthState) => {
      if (isMounted()) {
        setState(updater);
      }
    },
    [isMounted]
  );

  /**
   * Check the current authentication session
   */
  const checkSession = useCallback(async (): Promise<void> => {
    try {
      const response = await authService.getUser();

      if (isSuccessResponse(response)) {
        const userData = response.data;
        safeSetState((prev) => ({
          ...prev,
          user: userData ?? null,
          error: null,
          accessId: userData?.accessId ?? null,
        }));
      } else {
        safeSetState((prev) => ({
          ...prev,
          user: null,
          error: response.message ?? 'Authentication error. Please try again.',
          accessId: null,
        }));
      }
    } catch (err) {
      console.error('Unexpected session error:', err);
      safeSetState((prev) => ({
        ...prev,
        user: null,
        error: 'An unexpected error occurred. Please try again.',
        accessId: null,
      }));
    }
  }, [safeSetState]);

  /**
   * Login with username and password
   */
  const login = useCallback(
    async (
      accessId: string,
      username: string,
      password: string
    ): Promise<{ success: boolean; error?: string }> => {
      safeSetState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const response = await authService.signInWithPassword({ accessId, username, password });

        if (!isSuccessResponse(response)) {
          const errorMessage = response.message ?? 'Authentication failed';
          safeSetState((prev) => ({ ...prev, isLoading: false, error: errorMessage }));
          return { success: false, error: errorMessage };
        }

        await checkSession();
        safeSetState((prev) => ({ ...prev, isLoading: false }));
        return { success: true };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
        safeSetState((prev) => ({ ...prev, isLoading: false, error: errorMessage }));
        return { success: false, error: errorMessage };
      }
    },
    [checkSession, safeSetState]
  );

  /**
   * Logout the current user
   */
  const logout = useCallback(async (): Promise<void> => {
    safeSetState((prev) => ({ ...prev, isLoading: true }));
    try {
      await authService.signOut();
      safeSetState(() => ({ ...INITIAL_AUTH_STATE, isLoading: false }));
    } catch (err) {
      console.error('Logout error:', err);
      safeSetState((prev) => ({
        ...prev,
        isLoading: false,
        error: 'Failed to sign out. Please try again.',
      }));
    }
  }, [safeSetState]);

  /**
   * Reset password for an email
   */
  const resetPassword = useCallback(
    async (username: string, email: string): Promise<{ success: boolean; error?: string }> => {
      safeSetState((prev) => ({ ...prev, isLoading: true, error: null }));

      try {
        const response = await authService.resetPassword({ username, email });

        safeSetState((prev) => ({ ...prev, isLoading: false }));

        if (!isSuccessResponse(response)) {
          const errorMessage = response.message ?? 'Password reset failed';
          return { success: false, error: errorMessage };
        }

        return { success: true };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
        safeSetState((prev) => ({ ...prev, isLoading: false, error: errorMessage }));
        return { success: false, error: errorMessage };
      }
    },
    [safeSetState]
  );

  /**
   * Init session on first render
   */
  useEffect(() => {
    checkSession().finally(() => {
      safeSetState((prev) => ({ ...prev, isLoading: false }));
    });
  }, [checkSession, safeSetState]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      ...state,
      checkSession,
      login,
      logout,
      resetPassword,
    }),
    [state, checkSession, login, logout, resetPassword]
  );

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

export default AuthProvider;
