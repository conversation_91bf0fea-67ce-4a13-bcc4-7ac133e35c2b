import { act, render, renderHook, screen } from '@testing-library/react';
import type { ReactNode } from 'react';
import { describe, expect, it, vi } from 'vitest';
import {
  PageAlertStoreProvider,
  usePageAlertStore,
  type PageAlertStoreProviderProps,
} from './page-alert.context';
import '@testing-library/jest-dom';

// Test component that uses the hook
const TestComponent = ({ selector }: { selector?: (state: any) => any }) => {
  const data = usePageAlertStore(selector || ((state) => state));
  return <div data-testid="test-component">{JSON.stringify(data)}</div>;
};

// Wrapper component for testing
const createWrapper = (props?: Partial<PageAlertStoreProviderProps>) => {
  return ({ children }: { children: ReactNode }) => (
    <PageAlertStoreProvider {...props}>{children}</PageAlertStoreProvider>
  );
};

describe('PageAlertStoreProvider', () => {
  it('should render children without errors', () => {
    render(
      <PageAlertStoreProvider>
        <div data-testid="child">Test Child</div>
      </PageAlertStoreProvider>
    );

    expect(screen.getByTestId('child')).toBeInTheDocument();
    expect(screen.getByText('Test Child')).toBeInTheDocument();
  });

  it('should provide store context to children', () => {
    render(
      <PageAlertStoreProvider>
        <TestComponent />
      </PageAlertStoreProvider>
    );

    const component = screen.getByTestId('test-component');
    expect(component).toBeInTheDocument();

    // Should contain the initial store state
    const content = component.textContent;
    expect(content).toContain('"severity":"info"');
    expect(content).toContain('"title":""');
  });
});

describe('usePageAlertStore', () => {
  it('should return selected state from store', () => {
    const { result } = renderHook(() => usePageAlertStore((state) => state.display), {
      wrapper: createWrapper(),
    });

    expect(result.current).toBe(false); // default value
  });

  it('should return multiple selected values', () => {
    const { result } = renderHook(
      () => {
        const display = usePageAlertStore((state) => state.display);
        const title = usePageAlertStore((state) => state.title);
        const message = usePageAlertStore((state) => state.message);
        const severity = usePageAlertStore((state) => state.severity);

        return { display, title, message, severity };
      },
      { wrapper: createWrapper() }
    );

    expect(result.current.display).toBe(false);
    expect(result.current.title).toBe('');
    expect(result.current.message).toBe('');
    expect(result.current.severity).toBe('info');
  });

  it('should return entire state when no selector provided', () => {
    const { result } = renderHook(() => usePageAlertStore((state) => state), {
      wrapper: createWrapper(),
    });

    expect(result.current).toMatchObject({
      display: false,
      title: '',
      message: '',
      severity: 'info',
    });
    expect(typeof result.current.showAlert).toBe('function');
    expect(typeof result.current.closeAlert).toBe('function');
  });

  it('should update display when showAlert action is called', () => {
    const { result } = renderHook(
      () => {
        const display = usePageAlertStore((state) => state.display);
        const showAlert = usePageAlertStore((state) => state.showAlert);
        return { display, showAlert };
      },
      { wrapper: createWrapper() }
    );

    expect(result.current.display).toBe(false);

    act(() => {
      result.current.showAlert({ title: '', message: '', severity: 'success' });
    });

    expect(result.current.display).toBe(true);
  });

  it('should throw error when used outside provider', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      // eslint-disable-next-line sonarjs/no-nested-functions
      renderHook(() => usePageAlertStore((state) => state)); // NOSONAR
    }).toThrow('usePageAlertStore must be used within PageAlertStoreProvider');

    consoleSpy.mockRestore();
  });
});
