'use client';

import { createPageAlertStore, type PageAlertStore } from '@/store/page-alert/page-alert.store';
import { createContext, useContext, useRef, type ReactNode } from 'react';
import { useStore } from 'zustand';

/**
 * Type definition for the page alert store API
 * Represents the return type of createAlertStore function
 */
export type PageAlertStoreApi = ReturnType<typeof createPageAlertStore>;

/**
 * React context for sharing page alert store across components
 * Provides access to the Zustand store instance throughout the component tree
 */
export const PageAlertStoreContext = createContext<PageAlertStoreApi | undefined>(undefined);

/**
 * Props interface for PageAlertStoreProvider component
 */
export interface PageAlertStoreProviderProps {
  children: ReactNode;
}

/**
 * Provider component that makes alert store available to child components
 */
export const PageAlertStoreProvider = ({ children }: PageAlertStoreProviderProps) => {
  const storeRef = useRef<PageAlertStoreApi | null>(null);
  storeRef.current ??= createPageAlertStore();

  return (
    <PageAlertStoreContext.Provider value={storeRef.current}>
      {children}
    </PageAlertStoreContext.Provider>
  );
};

/**
 * Custom hook to access alert store with selector pattern
 */
export const usePageAlertStore = <T,>(selector: (store: PageAlertStore) => T): T => {
  const pageAlertStoreContext = useContext(PageAlertStoreContext);

  if (!pageAlertStoreContext) {
    throw new Error(`usePageAlertStore must be used within PageAlertStoreProvider`);
  }

  return useStore(pageAlertStoreContext, selector);
};
