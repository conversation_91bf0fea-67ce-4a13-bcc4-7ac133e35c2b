import { VerticalShellsLight } from '@/components/application-ui/shells/vertical-shells-light';
import { PageAlertStoreProvider } from '@/contexts/page-alert/page-alert.context';
import { withAuthGuard } from '@/hocs/with-auth-guard';
import getMenuItems from '@/router/menu-items';
import { type ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

interface LayoutProps {
  children?: ReactNode;
  menuItems?: MenuItem[];
}

const BaseLayout = (props: LayoutProps) => {
  const { t } = useTranslation();
  const defaultMenuItems = getMenuItems(t);

  return (
    <PageAlertStoreProvider>
      <VerticalShellsLight
        menuItems={props.menuItems ?? defaultMenuItems}
        {...props}
      />
    </PageAlertStoreProvider>
  );
};

export const Layout = withAuthGuard(BaseLayout);
