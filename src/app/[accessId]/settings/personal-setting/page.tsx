'use client';

import { FormLayout } from '@/components/application-ui/layouts/form-layout';
import {
  PersonalSettingForm,
  type FormAction,
} from '@/components/features/settings/personal-setting';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { transformText } from '@/utils/text-transform.util';
import { useRef, useState, type JSX } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Page metadata configuration for the personal settings page
 */
const pageMeta = {
  title: 'common.label.personalSetting',
  // description: 'settings.sentence.personalPageDesc',
};

/**
 * A page component that allows users to manage their personal settings including
 * appearance preferences, pagination settings, date/time formats, language, and timezone.
 */
const PersonalSettingPage = (): JSX.Element => {
  usePageTitle(pageMeta.title);

  const { t } = useTranslation();
  const translatedTitle = t(pageMeta.title);
  const transformedTitle = transformText(translatedTitle, 'sentenceCase');

  const formRef = useRef<FormAction>({});
  const [isDirty, setIsDirty] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const disableSubmit = isLoading || !isDirty || !formRef.current.submit;
  const disableReset = isLoading || !formRef.current.reset;

  return (
    <FormLayout
      title={transformedTitle}
      submit={{
        label: t('common.label.save'),
        disabled: disableSubmit,
        loading: isLoading,
        onClick: formRef.current?.submit,
      }}
      reset={{
        label: t('common.label.reset'),
        disabled: disableReset,
        onClick: formRef.current?.reset,
      }}
      unsaved={{ enabled: isDirty }}
    >
      <PersonalSettingForm
        setForm={({ submit, reset }) => {
          formRef.current.submit = submit;
          formRef.current.reset = reset;
        }}
        setDirty={(isDirty) => setIsDirty(isDirty)}
        setLoading={(isLoading) => setIsLoading(isLoading)}
      />
    </FormLayout>
  );
};

export default PersonalSettingPage;
