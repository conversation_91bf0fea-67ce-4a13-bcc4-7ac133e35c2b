import { PageAlert } from '@/components/application-ui/shells/vertical-shells-light/parts/page-alert';
import {
  PageAlertStoreProvider,
  usePageAlertStore,
} from '@/contexts/page-alert/page-alert.context';
import { mockErrorHandlers, mockSuccessHandlers } from '@/mocks/personal-settings.mock';
import i18n from '@/providers/i18n/i18n';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useEffect } from 'react';
import { expect, waitFor } from 'storybook/test';
import Page from './page';
import {
  withNavigationGuardProvider,
  withPersonalSettingStoreProvider,
  withQueryProvider,
} from '.storybook/decorators';

/**
 * Personal Setting Form component for managing user's personal settings
 */
const meta = {
  component: Page,
  parameters: { layout: 'fullscreen' },
  decorators: [
    withNavigationGuardProvider,
    withPersonalSettingStoreProvider,
    withQueryProvider,
    (Story) => {
      return (
        <PageAlertStoreProvider>
          <PageAlert placement="top">
            <Story />
          </PageAlert>
        </PageAlertStoreProvider>
      );
    },
  ],
} satisfies Meta<typeof Page>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default
 */
export const Default: Story = {
  parameters: {
    msw: {
      handlers: mockSuccessHandlers,
    },
  },
  play: async ({ canvas, userEvent, step }) => {
    const saveBtn = i18n.t('common.label.save');

    await step('Save button disabled with no changes', () => {
      expect(canvas.getByText(saveBtn)).toHaveAttribute('disabled');
    });

    await step('Save button enabled with changes', async () => {
      await waitFor(() =>
        userEvent.click(canvas.getByRole('button', { name: i18n.t('settings.label.light') }))
      );

      await waitFor(() => expect(canvas.getByText(saveBtn)).not.toHaveAttribute('disabled'));
    });

    await step('Reset button is functional', async () => {
      await userEvent.click(canvas.getByText(i18n.t('common.label.reset')));

      await waitFor(() => expect(canvas.getByText(saveBtn)).toHaveAttribute('disabled'));
    });
  },
};

/**
 * Success
 */
export const UpdateSuccess: Story = {
  parameters: {
    msw: {
      handlers: mockSuccessHandlers,
    },
  },
  render: () => {
    const SuccessPageComponent = () => {
      const { showAlert } = usePageAlertStore((state) => state);

      useEffect(() => {
        showAlert({ title: 'Title', message: 'Description sentence.', severity: 'success' });
      }, [showAlert]);

      return <Page></Page>;
    };
    return <SuccessPageComponent />;
  },
  play: async ({ canvas, userEvent, step }) => {
    const saveBtn = i18n.t('common.label.save');

    // Close the default alert
    await userEvent.click(canvas.getByTestId('CloseIcon'));

    await step('Alert is shown on submit success', async () => {
      await waitFor(() =>
        userEvent.click(canvas.getByRole('button', { name: i18n.t('settings.label.light') }))
      );

      await waitFor(() => expect(canvas.getByText(saveBtn)).not.toHaveAttribute('disabled'));

      await userEvent.click(canvas.getByText(saveBtn));

      await waitFor(() => expect(canvas.getByRole('alert')).toHaveClass('MuiAlert-colorSuccess'));
    });
  },
};

/**
 * Error
 */
export const UpdateError: Story = {
  parameters: {
    msw: {
      handlers: mockErrorHandlers,
    },
  },
  render: () => {
    const ErrorPageComponent = () => {
      const { showAlert } = usePageAlertStore((state) => state);

      useEffect(() => {
        showAlert({ title: 'Title', message: 'Description sentence.', severity: 'error' });
      }, [showAlert]);

      return <Page></Page>;
    };
    return <ErrorPageComponent />;
  },
  play: async ({ canvas, userEvent, step }) => {
    const saveBtn = i18n.t('common.label.save');

    // Close the default alert
    await userEvent.click(canvas.getByTestId('CloseIcon'));

    await step('Alert is shown on submit success', async () => {
      await waitFor(() =>
        userEvent.click(canvas.getByRole('button', { name: i18n.t('settings.label.light') }))
      );

      await waitFor(() => expect(canvas.getByText(saveBtn)).not.toHaveAttribute('disabled'));

      await userEvent.click(canvas.getByText(saveBtn));

      await waitFor(() => expect(canvas.getByRole('alert')).toHaveClass('MuiAlert-colorError'));
    });
  },
};
