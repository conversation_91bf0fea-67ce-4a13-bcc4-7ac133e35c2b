'use client';

import { USER } from '@/constants';
import i18n from '@/providers/i18n/i18n';
import ROUTES from '@/router/routes';
import { transformText } from '@/utils/text-transform.util';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, userEvent, waitFor, within } from 'storybook/test';
import Page from './page';

const meta = {
  title: 'Pages/Auth/LoginPage',
  component: Page,
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);
      return (
        <div style={{ width: '1200px', margin: '0 auto' }}>
          <Story />
        </div>
      );
    },
  ],
} satisfies Meta<typeof Page>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    await step('Should render form elements', async () => {
      expect(await canvas.findByTestId('accessId')).toBeInTheDocument();
      expect(await canvas.findByTestId('oauthAccessId')).toBeInTheDocument();
      expect(await canvas.findByTestId('username')).toBeInTheDocument();
      expect(await canvas.findByTestId('password')).toBeInTheDocument();
      expect(await canvas.findByTestId('forgotPassword')).toHaveAttribute(
        'href',
        ROUTES.AUTH['RESET_PASSWORD']
      );
      expect(await canvas.findByTestId('submit')).toBeInTheDocument();
      expect(await canvas.findByTestId('oauthSignIn')).toBeInTheDocument();
    });

    await step('Should show error alert for invalid credentials', async () => {
      const usernameInput = await canvas.findByTestId('username');
      const passwordInput = await canvas.findByTestId('password');
      await userEvent.clear(usernameInput);
      await userEvent.clear(passwordInput);

      await userEvent.type(usernameInput, 'wronguser');
      await userEvent.type(passwordInput, 'wrongp@ssword');
      await userEvent.click(await canvas.findByTestId('submit'));

      await waitFor(() => {
        expect(
          canvas.getByText(
            transformText(i18n.t('error.auth.sentence.invalidCredentials'), 'sentenceCase')
          )
        ).toBeInTheDocument();
      });
    });

    await step('Should reject invalid access ID input in normal login form', async () => {
      const accessIdInput = await canvas.findByTestId('accessId');
      await userEvent.clear(accessIdInput);
      await userEvent.type(accessIdInput, '123');
      await userEvent.click(await canvas.findByTestId('submit'));

      await waitFor(() => {
        expect(
          canvas.getByText(
            transformText(
              i18n.t('error.validation.sentence.exactLength', {
                attribute: i18n.t('common.label.accessId'),
                limit: USER.accessId.exactLength,
                type: i18n.t('common.label.number'),
              }),
              'sentenceCase'
            )
          )
        ).toBeInTheDocument();
      });

      await userEvent.clear(accessIdInput);
    });

    await step('Should reject invalid access ID input in oauth login form ', async () => {
      const oauthAccessIdInput = await canvas.findByTestId('oauthAccessId');
      await userEvent.clear(oauthAccessIdInput);
      await userEvent.type(oauthAccessIdInput, '456');
      await userEvent.click(await canvas.findByTestId('oauthSignIn'));

      await waitFor(() => {
        expect(
          canvas.getByText(
            transformText(
              i18n.t('error.validation.sentence.exactLength', {
                attribute: i18n.t('common.label.accessId'),
                limit: USER.accessId.exactLength,
                type: i18n.t('common.label.number'),
              }),
              'sentenceCase'
            )
          )
        ).toBeInTheDocument();
      });

      await userEvent.clear(oauthAccessIdInput);
    });

    await step('Should reject non-numeric input in access ID', async () => {
      const accessIdInput = await canvas.findByTestId('accessId');
      await userEvent.type(accessIdInput, 'testaccessid');

      const oauthAccessIdInput = await canvas.findByTestId('oauthAccessId');
      await userEvent.type(oauthAccessIdInput, 'testoauthaccessid');

      await waitFor(() => {
        expect(accessIdInput).toHaveValue('');
        expect(oauthAccessIdInput).toHaveValue('');
      });
    });

    await step('Should toggle password visibility', async () => {
      const passwordInput = await canvas.findByTestId('password');
      await userEvent.clear(passwordInput);
      await userEvent.type(passwordInput, 'wrongp@ssword');

      expect(passwordInput).toHaveAttribute('type', 'password');

      const toggleButton = await canvas.findByTestId('passwordToggle');
      await userEvent.click(toggleButton);

      expect(passwordInput).toHaveAttribute('type', 'text');
    });

    await step('Should support keyboard tabbing', async () => {
      const inputs = {
        accessId: await canvas.findByTestId('accessId'),
        username: await canvas.findByTestId('username'),
        password: await canvas.findByTestId('password'),
        toggle: await canvas.findByTestId('passwordToggle'),
        forgot: await canvas.findByTestId('forgotPassword'),
        submit: await canvas.findByTestId('submit'),
        oauthAccessId: await canvas.findByTestId('oauthAccessId'),
        oauthSignIn: await canvas.findByTestId('oauthSignIn'),
      };

      inputs.accessId.focus();
      expect(document.activeElement).toBe(inputs.accessId);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.username);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.password);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.toggle);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.forgot);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.submit);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.oauthAccessId);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.oauthSignIn);
    });

    await step('Should have href attribute for forgot password', async () => {
      const forgotLink = canvas.getByTestId('forgotPassword');
      expect(forgotLink).toBeInTheDocument();
      expect(forgotLink).toHaveAttribute('href', '/forgot-password');
    });
  },
};
