import { ForgotPasswordForm } from '@/components/features/auth';
import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';

const pushMock = vi.fn();

vi.mock('@/hooks/navigation/use-router.hook', () => ({
  useRouter: () => ({
    push: pushMock,
  }),
}));

describe('Forgot Password Form', () => {
  it('should return to sign in page when clicking the return to sign in button ', async () => {
    render(<ForgotPasswordForm />);

    const returnToSignInButton = screen.getByTestId('redirectButton');
    fireEvent.click(returnToSignInButton);

    expect(pushMock).toHaveBeenCalledWith('/login');
  });
});
