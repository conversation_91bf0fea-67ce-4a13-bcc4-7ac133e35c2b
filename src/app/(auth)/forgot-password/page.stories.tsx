import i18n from '@/providers/i18n/i18n';
import { transformText } from '@/utils/text-transform.util';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, userEvent, waitFor, within } from 'storybook/test';
import Page from './page';

const meta = {
  title: 'Pages/Auth/ForgotPasswordPage',
  component: Page,
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);
      return <Story />;
    },
  ],
} satisfies Meta<typeof Page>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Story for the default ForgotPasswordPage component.
 */
export const Default: Story = {
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    await step('Should render form elements', async () => {
      expect(await canvas.findByTestId('username')).toBeInTheDocument();
      expect(await canvas.findByTestId('email')).toBeInTheDocument();
      expect(await canvas.findByTestId('redirectButton')).toBeInTheDocument();
      expect(await canvas.findByTestId('submit')).toBeInTheDocument();
    });

    await step('Should show success alert for valid credentials', async () => {
      const usernameInput = await canvas.findByTestId('username');
      const emailWrapper = await canvas.findByTestId('email');
      const emailInput = within(emailWrapper).getByRole('textbox');
      await userEvent.clear(usernameInput);
      await userEvent.clear(emailInput);

      await userEvent.type(usernameInput, 'demouser');
      await userEvent.type(emailInput, '<EMAIL>');
      await userEvent.click(await canvas.findByTestId('submit'));

      await waitFor(() => {
        expect(canvas.getByText(/OTP has been sent to your email/i)).toBeInTheDocument();
      });
    });

    await step('Should show error alert for invalid credentials', async () => {
      const usernameInput = await canvas.findByTestId('username');
      const emailWrapper = await canvas.findByTestId('email');
      const emailInput = within(emailWrapper).getByRole('textbox');
      await userEvent.clear(usernameInput);
      await userEvent.clear(emailInput);

      await userEvent.type(usernameInput, 'wronguser');
      await userEvent.type(emailInput, '<EMAIL>');
      await userEvent.click(await canvas.findByTestId('submit'));

      const msg = transformText(i18n.t('error.auth.sentence.accountNotExist'), 'sentenceCase');
      const msgRegex = new RegExp(msg.replace(/\s+/g, '\\s+'), 'i');

      const matches = await canvas.findAllByText(
        (_, el) => !!el && msgRegex.test(el.textContent ?? '')
      );
      expect(matches.length).toBeGreaterThan(0);
    });
  },
};
