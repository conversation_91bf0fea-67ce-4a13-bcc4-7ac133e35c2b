import { SETTINGS_ROUTES } from '@/router/api-routes';
import { http, HttpResponse } from 'msw';

/**
 * Access levels available for settings configuration
 */
const accessLevels = ['root', 'organisation', 'merchant'];

/**
 * Base settings structure with common metadata fields
 */
const setting = {
  id: '',
  field: '',
  category: '',
  accessLevels: accessLevels,
  customSettings: {},
};

/**
 * Base custom settings structure with common metadata fields
 */
const customSettings = {
  value: '',
  version: '1',
  createdBy: null,
  updatedBy: 'd078a26f-14fe-4994-b4d4-ddeed1066f44',
  createdAt: '2025-07-01T12:34:56.852Z',
  updatedAt: '2025-07-01T12:34:56.852Z',
};

/**
 * Helper function to create a personal setting object with consistent structure
 */
const createPersonalSetting = (id: string, field: string, value: string) => ({
  ...setting,
  id: id,
  field: field,
  category: 'personal',
  customSettings: { ...customSettings, value },
});

/**
 * Mock response data for successful personal settings retrieval
 */
export const GET_PERSONAL_SETTINGS_RES = {
  message: 'Personal settings retrieved successfully',
  data: [
    createPersonalSetting('01b996c6-3b5b-11f0-9f6a-5bcd9b06a552', 'appearance', 'system'),
    createPersonalSetting('01b99748-3b5b-11f0-9f6a-9bb4731a14c8', 'recordPerPage', '25'),
    createPersonalSetting('01b997c0-3b5b-11f0-9f6a-f38f7e1b2de7', 'dateFormat', 'yyyy-mm-dd'),
    createPersonalSetting('01b998a6-3b5b-11f0-9f6a-4b2768f0ae1c', 'timeFormat', '24'),
    createPersonalSetting(
      '01b99a54-3b5b-11f0-9f6a-77ce7c68a7e8',
      'defaultLanguage',
      '9f160afc-685c-11f0-ac0c-3b1a740aad2b'
    ),
    createPersonalSetting(
      '01b99bda-3b5b-11f0-9f6a-93c36abda11d',
      'defaultTimezone',
      '9f17deb8-685c-11f0-ac0c-23e338582c39'
    ),
  ],
};

/**
 * Mock response data for personal settings options (dropdowns, selections)
 */
export const GET_PERSONAL_SETTINGS_OPTIONS_RES = {
  message: 'Successfully retrieved settings options',
  data: {
    appearanceModes: [
      { label: 'System', value: 'system' },
      { label: 'Light', value: 'light' },
      { label: 'Dark', value: 'dark' },
    ],
    preferences: [
      { label: '25', value: '25' },
      { label: '100', value: '100' },
      { label: '150', value: '150' },
      { label: '200', value: '200' },
    ],
    dateFormat: [
      { label: 'YYYY-MM-DD', value: 'yyyy-mm-dd' },
      { label: 'YYYY/MM/DD', value: 'yyyy/mm/dd' },
      { label: 'YYYY.MM.DD', value: 'yyyy.mm.dd' },
      { label: 'DD-MM-YYYY', value: 'dd-mm-yyyy' },
      { label: 'DD/MM/YYYY', value: 'dd/mm/yyyy' },
      { label: 'DD.MM.YYYY', value: 'dd.mm.yyyy' },
    ],
    timeFormat: [
      { label: '12-hour', value: '12' },
      { label: '24-hour', value: '24' },
    ],
    language: [
      { label: 'English', value: '9f160afc-685c-11f0-ac0c-3b1a740aad2b', code: 'EN' },
      {
        label: 'Chinese (Simplified)',
        value: '9f1608ea-685c-11f0-ac0c-af64e4d09be3',
        code: 'ZH-CN',
      },
      { label: 'Vietnamese', value: '9f1684be-685c-11f0-ac0c-3b783304e5c9', code: 'VI' },
      { label: 'Urdu', value: '9f168428-685c-11f0-ac0c-4fe5974c92a9', code: 'UR' },
      { label: 'Thai', value: '9f168356-685c-11f0-ac0c-07ecc86f9aac', code: 'TH' },
      { label: 'Spanish', value: '9f167834-685c-11f0-ac0c-536d6ab43f9d', code: 'ES' },
      { label: 'Malay', value: '9f167794-685c-11f0-ac0c-abc6f0f036db', code: 'MS' },
      { label: 'Italian', value: '9f1676fe-685c-11f0-ac0c-5f3ac02f35ee', code: 'IT' },
      { label: 'Indonesian', value: '9f16765e-685c-11f0-ac0c-abe782d6039e', code: 'ID' },
      { label: 'Hindi', value: '9f1675b4-685c-11f0-ac0c-8ba68327036e', code: 'HI' },
      { label: 'German', value: '9f16751e-685c-11f0-ac0c-d74809fcdf74', code: 'DE' },
      { label: 'French', value: '9f16746a-685c-11f0-ac0c-935677c108f9', code: 'FR' },
    ],
    region: [
      { label: 'Europe/Paris', value: '9f17e08e-685c-11f0-ac0c-6bc1c9932c87' },
      { label: 'Europe/London', value: '9f17dfe4-685c-11f0-ac0c-ffb6c4ffa192' },
      { label: 'Europe/Berlin', value: '9f17df4e-685c-11f0-ac0c-eb9ec64163d9' },
      { label: 'Asia/Singapore', value: '9f17deb8-685c-11f0-ac0c-23e338582c39' },
      { label: 'Asia/Manila', value: '9f17de2c-685c-11f0-ac0c-8f5337d267fa' },
      { label: 'Asia/Kuala_Lumpur', value: '9f17dd8c-685c-11f0-ac0c-4fb07da44672' },
      { label: 'Asia/Karachi', value: '9f17dcf6-685c-11f0-ac0c-474552a66573' },
      { label: 'Asia/Jakarta', value: '9f17dbca-685c-11f0-ac0c-b3e86e50ad1c' },
      { label: 'Asia/Hong_Kong', value: '9f17db34-685c-11f0-ac0c-53c1e1db83c4' },
      { label: 'Asia/Ho_Chi_Minh', value: '9f17da94-685c-11f0-ac0c-0bc4c528adb6' },
    ],
  },
};

/**
 * Mock response data for failed get requests
 */
export const GET_FAIL_SETTINGS_RES = {
  message: 'validation error',
  errorCode: 'FST_ERR_VALIDATION',
  meta: { details: [{ category: 'category is required.' }] },
};

/**
 * Mock response data for successful update requests
 */
export const UPDATE_SETTINGS_RES = {
  message: 'Successfully updated settings personal',
};

/**
 * Mock response data for failed update requests
 */
export const UPDATE_FAIL_SETTINGS_RES = {
  message:
    'The record has been modified since last retrieval. Please refresh and try again. (Current Version: 1)',
  errorCode: '10013',
  meta: {},
};

/**
 * MSW handlers for successful GET requests to settings endpoints
 */
export const mockGetHandlers = [
  http.get(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.PERSONAL}`, () =>
    HttpResponse.json(GET_PERSONAL_SETTINGS_RES)
  ),
  http.get(
    `${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.CATEGORY_OPTIONS('personal')}`,
    () => HttpResponse.json(GET_PERSONAL_SETTINGS_OPTIONS_RES)
  ),
];

/**
 * MSW handlers for failed GET requests to settings endpoints
 */
export const mockGetFailHandlers = [
  http.get(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.PERSONAL}`, () =>
    HttpResponse.json(GET_FAIL_SETTINGS_RES, { status: 400 })
  ),
  http.get(
    `${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.CATEGORY_OPTIONS('personal')}`,
    () => HttpResponse.json(GET_FAIL_SETTINGS_RES, { status: 400 })
  ),
];

/**
 * MSW handlers for successful PUT requests to settings endpoints
 */
export const mockUpdateHandlers = [
  http.put(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.PERSONAL}`, () =>
    HttpResponse.json(UPDATE_SETTINGS_RES)
  ),
  http.put(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.THEMES}`, () =>
    HttpResponse.json(UPDATE_SETTINGS_RES)
  ),
  http.put(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.SAFETY}`, () =>
    HttpResponse.json(UPDATE_SETTINGS_RES)
  ),
];

/**
 * MSW handlers for failed PUT requests to settings endpoints
 */
export const mockUpdateFailHandlers = [
  http.put(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.PERSONAL}`, () =>
    HttpResponse.json(UPDATE_FAIL_SETTINGS_RES, { status: 409 })
  ),
  http.put(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.THEMES}`, () =>
    HttpResponse.json(UPDATE_FAIL_SETTINGS_RES, { status: 409 })
  ),
  http.put(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.SAFETY}`, () =>
    HttpResponse.json(UPDATE_FAIL_SETTINGS_RES, { status: 409 })
  ),
];

/**
 * Combined handlers for all successful settings operations (GET + PUT)
 */
export const mockSuccessHandlers = mockGetHandlers.concat(mockUpdateHandlers);

/**
 * Combined handlers for mixed scenarios (successful GET + failed PUT)
 */
export const mockErrorHandlers = mockGetHandlers.concat(mockUpdateFailHandlers);
