import { beforeEach, describe, expect, it } from 'vitest';
import { createPersonalSettingStore } from './personal-setting.store';

describe('createPersonalSettingStore', () => {
  let store: ReturnType<typeof createPersonalSettingStore>;

  beforeEach(() => {
    store = createPersonalSettingStore();
  });

  it('should initialize with default values', () => {
    const state = store.getState();

    expect(state.version).toBe(0);
    expect(state.appearance).toBe('');
    expect(state.recordPerPage).toBe('');
    expect(state.dateFormat).toBe('');
    expect(state.timeFormat).toBe('');
    expect(state.defaultLanguage).toBe('');
    expect(state.defaultTimezone).toBe('');
    expect(typeof state.update).toBe('function');
  });

  it('should have all required properties', () => {
    const state = store.getState();

    expect(state).toHaveProperty('version');
    expect(state).toHaveProperty('appearance');
    expect(state).toHaveProperty('recordPerPage');
    expect(state).toHaveProperty('dateFormat');
    expect(state).toHaveProperty('timeFormat');
    expect(state).toHaveProperty('defaultLanguage');
    expect(state).toHaveProperty('defaultTimezone');
    expect(state).toHaveProperty('update');
  });

  it('should update single property', () => {
    const { update } = store.getState();

    update({ appearance: 'dark' });

    const updatedState = store.getState();
    expect(updatedState.appearance).toBe('dark');
    // Other properties unchanged
    expect(updatedState.version).toBe(0);
  });

  it('should update multiple properties', () => {
    const { update } = store.getState();

    update({
      appearance: 'light',
      recordPerPage: '25',
    });

    const updatedState = store.getState();
    expect(updatedState.appearance).toBe('light');
    expect(updatedState.recordPerPage).toBe('25');
    // Other properties unchanged
    expect(updatedState.version).toBe(0);
  });

  it('should create independent store instances', () => {
    const store1 = createPersonalSettingStore();
    const store2 = createPersonalSettingStore();

    store1.getState().update({ appearance: 'dark' });
    store2.getState().update({ appearance: 'light' });

    expect(store1.getState().appearance).toBe('dark');
    expect(store2.getState().appearance).toBe('light');
  });
});
