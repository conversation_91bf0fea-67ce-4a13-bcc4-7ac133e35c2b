import type { PersonalFormData } from '@/services/settings/setting.type';
import type { StateCreator } from 'zustand';
import { devtools } from 'zustand/middleware';
import { createStore } from 'zustand/vanilla';

/**
 * State interface for personal settings store
 */
export type PersonalSettingState = {
  languageMap: Record<Language, Uuid> | undefined;
} & PersonalFormData;

/**
 * Actions interface for personal settings store
 *
 * Updates the personal setting state or the form data with provided values
 */
export type PersonalSettingActions = {
  update: (values: Partial<PersonalSettingState>) => void;
};

/**
 * Combined store interface containing both state and actions
 */
export type PersonalSettingStore = PersonalSettingState & PersonalSettingActions;

/**
 * Initial value for personal setting store
 */
const initialValue = {
  version: 0,
  appearance: '',
  recordPerPage: '',
  dateFormat: '',
  timeFormat: '',
  defaultLanguage: '',
  defaultTimezone: '',
};

/**
 * Creates a new personal setting store instance using Zustand
 *
 * This store manages personal user settings including appearance preferences,
 * pagination settings, date/time formats, language, and timezone configurations.
 */
export const createPersonalSettingStore = () => {
  const baseStore: StateCreator<PersonalSettingStore, [], [], PersonalSettingStore> = (set) => ({
    ...initialValue,
    languageMap: undefined,
    update: (values) => set((state) => ({ ...state, ...values })),
  });

  // Devtools only in development
  return process.env.NODE_ENV === 'development'
    ? createStore(
        devtools(baseStore, { name: 'PersonalSettingStore' }) as StateCreator<
          PersonalSettingStore,
          [],
          [],
          PersonalSettingStore
        >
      )
    : createStore(baseStore);
};
