import { beforeEach, describe, expect, it } from 'vitest';
import { createPageAlertStore } from './page-alert.store';

describe('createPageAlertStore', () => {
  let store: ReturnType<typeof createPageAlertStore>;

  beforeEach(() => {
    store = createPageAlertStore();
  });

  it('should initialize with default values', () => {
    const state = store.getState();

    expect(state.display).toEqual(false);
    expect(state.title).toEqual('');
    expect(state.message).toEqual('');
    expect(state.severity).toEqual('info');
    expect(typeof state.showAlert).toEqual('function');
    expect(typeof state.closeAlert).toEqual('function');
  });

  it('should have all required properties', () => {
    const state = store.getState();

    expect(state).toHaveProperty('display');
    expect(state).toHaveProperty('title');
    expect(state).toHaveProperty('message');
    expect(state).toHaveProperty('severity');
    expect(state).toHaveProperty('showAlert');
    expect(state).toHaveProperty('closeAlert');
  });

  it('should update display to true when call showAlert', () => {
    const { display, showAlert } = store.getState();

    expect(display).toEqual(false);

    showAlert({ title: 'alert', message: 'message', severity: 'success' });

    const updatedState = store.getState();
    expect(updatedState.title).toEqual('alert');
    expect(updatedState.message).toEqual('message');
    expect(updatedState.severity).toEqual('success');
    expect(updatedState.display).toEqual(true);
  });

  it('should update display to false when call closeAlert', () => {
    const { showAlert, closeAlert } = store.getState();

    showAlert({ title: 'alert', message: 'message', severity: 'success' });

    const state = store.getState();
    expect(state.display).toEqual(true);

    closeAlert();

    const updatedState = store.getState();
    expect(updatedState.display).toEqual(false);
  });
});
