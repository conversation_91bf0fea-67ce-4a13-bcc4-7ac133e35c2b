import type { AlertData } from '@/constants/alert.constant';
import type { AlertColor } from '@mui/material/Alert';
import { devtools } from 'zustand/middleware';
import { createStore, type StateCreator } from 'zustand/vanilla';

/**
 * State interface for page alert store
 */
export type PageAlertState = {
  display: boolean;
} & AlertData;

/**
 * Actions interface for page alert store
 */
export type PageAlertActions = {
  showAlert: (values: AlertData) => void;
  closeAlert: () => void;
};

/**
 * Combined store interface containing both state and actions
 */
export type PageAlertStore = PageAlertState & PageAlertActions;

/**
 * Initial value for alert store
 */
const initialValue: PageAlertState = {
  title: '',
  message: '',
  severity: 'info' as AlertColor,
  display: false,
};

/** Base initializer (typed without middleware) */
const initializer: StateCreator<PageAlertStore, [], []> = (set) => ({
  ...initialValue,
  showAlert: (values) => set((state) => ({ ...state, ...values, display: true })),
  closeAlert: () => set({ display: false }),
});

/**
 * Creates a new alert store instance using Zustand
 */
export const createPageAlertStore = () => {
  if (process.env.NODE_ENV === 'development') {
    // 👇 tell createStore that devtools middleware is present
    return createStore<PageAlertStore>()(devtools(initializer, { name: 'PageAlertStore' }));
  }
  // 👇 no middleware in prod
  return createStore<PageAlertStore>()(initializer);
};
