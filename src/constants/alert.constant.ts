import { generalAlertMap } from '@/errors/general.error';
import { type AlertColor } from '@mui/material/Alert';
import { type TFunction } from 'i18next';

export type AlertData = {
  severity: AlertColor;
  title: string;
  message?: string;
};

export type AlertMap = Record<string, AlertData>;

/**
 * This function retrieves an alert object based on the provided error code and message.
 * If a custom alert map is provided, it will be used to generate the alert object.
 * If no custom alert map is provided, a default alert object will be created with an error severity,
 * a 'Failed' title, and a fallback message.
 *
 * @param errorCode - The error code to use for generating the alert object. Optional.
 * @param message - The custom message to use for generating the alert object. Optional.
 * @param alertMap - A custom alert map to resolve the alert object based on the error code and message. Optional.
 *
 * @returns An AlertData object representing the alert to be displayed.
 *
 * @example
 * ```typescript
 * const customAlertMap: AlertMap = {
 *   '404': { severity: 'warning', title: 'error.label.notFound', message: 'error.sentence.notFound' },
 *   'default': { severity: 'error', title: 'error.label.failed' }
 * };
 *
 * const alert = getAlertFromErrorCode(tFunction, '404', 'Custom error message', customAlertMap);
 * console.log(alert); // Output: { severity: 'warning', title: 'Not Found', message: 'The requested resource was not found.' }
 * ```
 */
export const getAlertFromErrorCode = (
  t: TFunction,
  errorCode?: string,
  message?: string,
  alertMap?: AlertMap
): AlertData => {
  const fallbackMessage = message ?? 'error.sentence.internal';

  let alertInfo;
  if (errorCode?.startsWith('10')) {
    alertInfo = generalAlertMap[errorCode] ?? generalAlertMap.default;
  } else {
    alertInfo = alertMap ? (alertMap[errorCode!] ?? alertMap.default) : undefined;
  }

  return {
    severity: alertInfo?.severity ?? 'error',
    title: t(alertInfo?.title ?? 'error.label.failed'),
    message: message ?? t(alertInfo?.message ?? fallbackMessage),
  };
};
