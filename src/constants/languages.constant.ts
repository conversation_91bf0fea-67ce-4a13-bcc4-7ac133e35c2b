/**
 * Language option structure
 */
export interface LanguageOption {
  /**
   * Country code for the flag
   */
  icon: string;

  /**
   * Display label for the language
   */
  label: string;

  /**
   * The const code for the language
   */
  code: string;
}

/**
 * Available language options with their display information
 */
export const LANGUAGE_OPTIONS: Record<Language, LanguageOption> = {
  en: { icon: 'US', label: 'English', code: 'EN' },
  zh: { icon: 'CN', label: '简体中文', code: 'ZH-CN' },
};

export default LANGUAGE_OPTIONS;
