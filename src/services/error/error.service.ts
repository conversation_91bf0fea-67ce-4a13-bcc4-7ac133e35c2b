import type { ErrorService } from '@/services/error/error.type';
import type { ErrorResponse } from '@/types/api-responses.type';
import { AxiosError } from 'axios';

/**
 * Type guard to check if an error is an ApiError
 */
export const isApiError = (error: unknown): error is ApiError => {
  return error instanceof ApiError;
};

/**
 * Custom error class for API-related errors that includes an error code
 * along with the standard error message.
 */
export class ApiError extends Error {
  public readonly errorCode: string;

  constructor(message: string, errorCode: string) {
    super(message);
    this.name = 'ApiError';
    this.errorCode = errorCode;
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError);
    }
  }
}

/**
 * Type guard to check if an error is an AxiosError
 */
export const isAxiosError = (error: unknown): error is AxiosError => {
  return (error as AxiosError)?.isAxiosError === true;
};

/**
 * Error service for handling API errors
 */
class ErrorServiceImpl implements ErrorService {
  /**
   * Handles and standardizes error responses from axios error
   *
   * @param error - The axios error to be handled
   * @returns A standardized error response object
   */
  handleError = (error: unknown): ErrorResponse => {
    if (!isAxiosError(error)) {
      return {
        message: 'An unexpected error occurred',
        errorCode: 'UNKNOWN_ERROR',
      };
    }

    const responseData = error.response?.data as
      | { message?: string; errorCode?: string }
      | undefined;

    return {
      message: responseData?.message ?? error.message,
      errorCode: responseData?.errorCode ?? 'UNKNOWN_API_ERROR',
    };
  };
}

export const errorService = new ErrorServiceImpl();
