import ROUTES from '@/router/routes';
import type { ApiResponse } from '@/types/api-responses.type';
import type { User } from '../users/user.type';
import type {
  AuthService,
  ForgotPasswordParams,
  GoogleLoginParams,
  LoginParams,
  LoginResponse,
} from './auth.type';

/**
+ * Centralised storage keys & basic environment guards (SSR-safe)
+ */
const STORAGE_KEYS = {
  AUTH: 'uifort-authentication',
  PENDING_APPROVAL: 'pendingApproval',
} as const;

/**
 * Generate a random token for demo purposes
 */
const generateToken = (): string => {
  const arr = new Uint8Array(12);
  window.crypto.getRandomValues(arr);
  return Array.from(arr, (v) => v.toString(16).padStart(2, '0')).join('');
};

/**
 * Mock user data for demo purposes
 */
const mockUser: User = {
  id: '489567',
  avatar: '/avatars/2.png',
  firstName: '<PERSON>',
  lastName: '<PERSON>',
  username: 'clar<PERSON><PERSON><PERSON><PERSON>',
  email: '<EMAIL>',
  accessId: '123456789012',
  role: 'Administrator',
  jobtitle: 'Principal Engineer',
};

/**
 * Authentication service implementation
 *
 * This is currently a mock implementation for demo purposes.
 * In a real application, this would make actual API calls.
 */
class AuthServiceImpl implements AuthService {
  /**
   * Sign in with username and password
   *
   * @param params - Login parameters
   * @returns API response
   */
  signInWithPassword = async (params: LoginParams): Promise<ApiResponse<LoginResponse>> => {
    // In a real application, this would be an API call
    // return apiRequest('/auth/login', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(params),
    // });

    // Mock implementation for demo
    const { accessId, username, password } = params;

    if (username !== '<EMAIL>' || password !== 'DemoPass123') {
      return {
        message:
          'the username or password is incorrect. please enter a valid username and password to try again',
        errorCode: '20001',
        meta: {},
      };
    }

    const token = generateToken();
    localStorage.setItem(STORAGE_KEYS.AUTH, token);

    return {
      message: 'login successful',
      data: {
        accessId: accessId ?? '', // In a real application, this would be returned from the API response
      },
      meta: {},
    };
  };

  signInWithGoogle = async (params: GoogleLoginParams): Promise<ApiResponse<LoginResponse>> => {
    // In a real application, this would be an API call sending the Google token to the backend and verifying it with Google

    // Mock implementation for demo
    const { accessId, idToken } = params;

    if (!idToken) {
      return {
        message: 'Google login failed due to unknown credential',
        errorCode: '20009',
        meta: {},
      };
    }

    // In a real application, you would check if the user is already approved by the admin
    // Cache the value to avoid repeated synchronous storage calls.
    const pending = localStorage.getItem(STORAGE_KEYS.PENDING_APPROVAL);
    if (pending && pending === 'false') {
      const token = generateToken();
      localStorage.setItem(STORAGE_KEYS.AUTH, token);
    } else {
      localStorage.setItem(STORAGE_KEYS.PENDING_APPROVAL, 'true');

      return {
        message: 'Google login is pending for approval',
        errorCode: '20010',
        meta: {},
      };
    }

    return {
      message: 'login successful',
      data: {
        accessId: accessId ?? '',
      },
      meta: {},
    };
  };

  /**
   * Reset password
   *
   * @param params - Reset password parameters
   * @returns API response
   */
  resetPassword = async (params: ForgotPasswordParams): Promise<ApiResponse<null>> => {
    // In a real application, this would be an API call
    // return apiRequest('/auth/reset-password', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(params),
    // });

    // Mock implementation for demo
    const { username, email } = params;

    if (username !== 'demouser' || email !== '<EMAIL>') {
      return {
        message: 'Account does not exist.',
        errorCode: '20011',
        meta: {},
      };
    }

    return {
      message: 'OTP has been sent to your email',
      data: null,
      meta: {},
    };
  };

  /**
   * Get current user
   *
   * @returns API response with user data
   */
  getUser = async (): Promise<ApiResponse<User | null>> => {
    // In a real application, this would be an API call
    // return apiRequest('/auth/me', {
    //   headers: withAuth(),
    // });

    // Mock implementation for demo
    const token = localStorage.getItem(STORAGE_KEYS.AUTH);

    if (!token) {
      return {
        message: 'No active session found',
        data: null,
        meta: {},
      };
    }

    return {
      message: 'User session retrieved successfully',
      data: mockUser,
      meta: {},
    };
  };

  /**
   * Sign out
   *
   * @returns API response
   */
  signOut = async (): Promise<ApiResponse<void>> => {
    // In a real application, this would be an API call
    // return apiRequest('/auth/logout', {
    //   method: 'POST',
    //   headers: withAuth(),
    // });

    // Mock implementation for demo
    localStorage.removeItem(STORAGE_KEYS.AUTH);

    window.location.replace(ROUTES.AUTH.LOGIN);

    return {
      message: 'Signed out successfully',
      data: undefined,
      meta: {},
    };
  };
}

export const authService = new AuthServiceImpl();
