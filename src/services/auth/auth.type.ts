import type { ApiResponse } from '@/types/api-responses.type';
import type { User } from '../users/user.type';

/**
 * Login parameters
 */
export interface LoginParams {
  accessId?: string;
  username: string;
  password: string;
}

/**
 * Reset password parameters
 */
export interface ForgotPasswordParams {
  username: string;
  email: string;
}

export interface GoogleLoginParams {
  accessId?: string;
  idToken: string;
}

export interface LoginResponse {
  accessId: string;
  // add more fields in future as needed
}

/**
 * Auth service interface
 */
export interface AuthService {
  /**
   * Sign in with email and password
   */
  signInWithPassword(params: LoginParams): Promise<ApiResponse<LoginResponse>>;

  /**
   * Sign in with Google
   */
  signInWithGoogle(params: GoogleLoginParams): Promise<ApiResponse<LoginResponse>>;

  /**
   * Reset password
   */
  resetPassword(params: ForgotPasswordParams): Promise<ApiResponse<void>>;

  /**
   * Get current user
   */
  getUser(): Promise<ApiResponse<User | null>>;

  /**
   * Sign out
   */
  signOut(): Promise<ApiResponse<void>>;
}
