import type { ApiResponse } from '@/types/api-responses.type';
import type { Pid, Version } from '@/types/generic.type';

/**
 * Available setting categories
 */
export type SettingCategory = 'personal' | 'safety' | 'themes' | 'general';

/**
 * Available access levels
 */
export type AccessLevel = 'root' | 'organisation' | 'merchant';

/**
 * Custom setting response type
 */
export interface CustomSetting extends Version {
  value: string;
  createdBy: Uuid;
  updatedBy: Uuid;
  createdAt: string;
  updatedAt: string;
}

/**
 * System setting response type
 */
export interface Setting extends Pid {
  category: SettingCategory;
  field: string;
  accessLevel: readonly AccessLevel[];
  customSettings?: CustomSetting;
}

/** Option item */
export interface OptionItem {
  label: string;
  value: string;
  code?: string;
}

/**
 * System setting option response type
 */
export type SettingOption = Record<string, OptionItem[]>;
/**
 * Personal setting update data type
 */
export interface PersonalFormData extends Version {
  appearance: string;
  recordPerPage: string;
  dateFormat: string;
  timeFormat: string;
  defaultLanguage: Uuid;
  defaultTimezone: Uuid;
}

/**
 * Safety setting update data type
 */
export interface SafetyFormData extends Version {
  sessionLifetimeHours: string;
  passwordExpiryDays: string;
  passwordReuseCount: string;
  passwordMaximumAttempts: string;
  twoFactorSessionTimeoutDays: string;
}

/**
 * Themes setting update data type
 */
export interface ThemesFormData extends Version {
  toastMessagePosition: string;
  alertBarPosition: string;
}

/**
 * Union type for all setting form data types
 */
export type SettingFormData = PersonalFormData | SafetyFormData | ThemesFormData;

/**
 * Setting service interface
 */
export interface SettingService {
  /**
   * Retrieves settings for a specific category
   *
   * @param category - The setting category to retrieve
   * @returns Promise resolving to an array of settings
   */
  getSettings(category: SettingCategory): Promise<ApiResponse<Setting[]>>;

  /**
   * Retrieves available options for settings in a specific category
   *
   * @param category - The setting category to get options for
   * @returns Promise resolving to setting options
   */
  getSettingOptions(category: SettingCategory): Promise<ApiResponse<SettingOption>>;

  /**
   * Updates personal settings for the current user
   *
   * @param formData - Personal setting form data
   * @returns Promise resolving to the update result
   */
  updatePersonalSetting(formData: PersonalFormData): Promise<ApiResponse>;

  /**
   * Updates safety and security settings
   *
   * @param formData - Safety setting form data
   * @returns Promise resolving to the update result
   */
  updateSafetySetting(formData: SafetyFormData): Promise<ApiResponse>;

  /**
   * Updates theme and UI settings
   *
   * @param formData - Theme setting form data
   * @returns Promise resolving to the update result
   */
  updateThemesSetting(formData: ThemesFormData): Promise<ApiResponse>;
}
