/* eslint-disable sonarjs/no-hardcoded-passwords */
import {
  GET_PERSONAL_SETTINGS_OPTIONS_RES,
  GET_PERSONAL_SETTINGS_RES,
  mockSuccessHandlers,
  UPDATE_SETTINGS_RES,
} from '@/mocks/personal-settings.mock';
import axiosInstance from '@/providers/axios/axios';
import { SETTINGS_ROUTES } from '@/router/api-routes';
import { ApiError, errorService } from '@/services/error/error.service';
import { http, HttpResponse } from 'msw';
import { setupServer } from 'msw/node';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { settingService } from './setting.service';

describe('Setting service', () => {
  const server = setupServer(...mockSuccessHandlers);

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }));

  const MOCK_PERSONAL_SETTINGS_TEST_DATA = {
    version: 1,
    appearance: 'system',
    recordPerPage: '25',
    dateFormat: 'yyyy-mm-dd',
    timeFormat: '24',
    defaultLanguage: '123e4567-e89b-12d3-a456-426614174000',
    defaultTimezone: '123e4567-e89b-12d3-a456-426614174001',
  };

  // Safe: mock config value, not a password. NOSONAR
  const MOCK_SAFETY_SETTINGS_TEST_DATA = {
    version: 1,
    sessionLifetimeHours: '8',
    passwordExpiryDays: '90', // NOSONAR
    passwordReuseCount: '5', // NOSONAR
    passwordMaximumAttempts: '3', // NOSONAR
    twoFactorSessionTimeoutDays: '30',
  };

  const MOCK_THEMES_SETTINGS_TEST_DATA = {
    version: 1,
    toastMessagePosition: 'top-right',
    alertBarPosition: 'top',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(axiosInstance, 'get');
    vi.spyOn(axiosInstance, 'put');
    vi.spyOn(errorService, 'handleError');

    server.resetHandlers();
  });

  afterAll(() => server.close());

  describe('getSettings', () => {
    it('should call axios with correct endpoint and return data', async () => {
      const category = 'personal';
      const result = await settingService.getSettings(category);

      expect(axiosInstance.get).toHaveBeenCalledWith(SETTINGS_ROUTES.CATEGORY(category));
      expect(result).toEqual(GET_PERSONAL_SETTINGS_RES);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };
      const category = 'personal';

      server.use(
        http.get(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.CATEGORY(category)}`, () =>
          HttpResponse.json(mockErrorRes, { status: 400 })
        )
      );

      try {
        await settingService.getSettings(category);
      } catch (error: any) {
        expect(error).toBeInstanceOf(ApiError);
        expect(error.message).toBe(mockErrorRes.message);
        expect(error.errorCode).toBe(mockErrorRes.errorCode);
      }

      expect(errorService.handleError).toHaveBeenCalledOnce();
    });
  });

  describe('getSettingOptions', () => {
    it('should call axios with correct endpoint and return data', async () => {
      const category = 'personal';
      const result = await settingService.getSettingOptions(category);

      expect(axiosInstance.get).toHaveBeenCalledWith(SETTINGS_ROUTES.CATEGORY_OPTIONS(category));
      expect(result).toEqual(GET_PERSONAL_SETTINGS_OPTIONS_RES);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };
      const category = 'personal';

      server.use(
        http.get(
          `${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.CATEGORY_OPTIONS(category)}`,
          () => HttpResponse.json(mockErrorRes, { status: 400 })
        )
      );

      try {
        await settingService.getSettingOptions(category);
      } catch (error: any) {
        expect(error).toBeInstanceOf(ApiError);
        expect(error.message).toBe(mockErrorRes.message);
        expect(error.errorCode).toBe(mockErrorRes.errorCode);
      }

      expect(errorService.handleError).toHaveBeenCalledOnce();
    });
  });

  describe('updatePersonalSetting', () => {
    it('should call axios with correct endpoint and return data', async () => {
      const result = await settingService.updatePersonalSetting(MOCK_PERSONAL_SETTINGS_TEST_DATA);

      expect(axiosInstance.put).toHaveBeenCalledWith(
        SETTINGS_ROUTES.PERSONAL,
        MOCK_PERSONAL_SETTINGS_TEST_DATA
      );
      expect(result).toEqual(UPDATE_SETTINGS_RES);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };

      server.use(
        http.put(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.PERSONAL}`, () =>
          HttpResponse.json(mockErrorRes, { status: 400 })
        )
      );

      try {
        await settingService.updatePersonalSetting(MOCK_PERSONAL_SETTINGS_TEST_DATA);
      } catch (error: any) {
        expect(error).toBeInstanceOf(ApiError);
        expect(error.message).toBe(mockErrorRes.message);
        expect(error.errorCode).toBe(mockErrorRes.errorCode);
      }

      expect(errorService.handleError).toHaveBeenCalledOnce();
    });
  });

  describe('updateSafetySetting', () => {
    it('should call axios with correct endpoint and return data', async () => {
      const result = await settingService.updateSafetySetting(MOCK_SAFETY_SETTINGS_TEST_DATA);

      expect(axiosInstance.put).toHaveBeenCalledWith(
        SETTINGS_ROUTES.SAFETY,
        MOCK_SAFETY_SETTINGS_TEST_DATA
      );
      expect(result).toEqual(UPDATE_SETTINGS_RES);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };

      server.use(
        http.put(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.SAFETY}`, () =>
          HttpResponse.json(mockErrorRes, { status: 400 })
        )
      );

      try {
        await settingService.updateSafetySetting(MOCK_SAFETY_SETTINGS_TEST_DATA);
      } catch (error: any) {
        expect(error).toBeInstanceOf(ApiError);
        expect(error.message).toBe(mockErrorRes.message);
        expect(error.errorCode).toBe(mockErrorRes.errorCode);
      }

      expect(errorService.handleError).toHaveBeenCalledOnce();
    });
  });

  describe('updateThemesSetting', () => {
    it('should call axios with correct endpoint and return data', async () => {
      const result = await settingService.updateThemesSetting(MOCK_THEMES_SETTINGS_TEST_DATA);

      expect(axiosInstance.put).toHaveBeenCalledWith(
        SETTINGS_ROUTES.THEMES,
        MOCK_THEMES_SETTINGS_TEST_DATA
      );
      expect(result).toEqual(UPDATE_SETTINGS_RES);
    });

    it('should call errorService when axios throw error', async () => {
      const mockErrorRes = { message: 'error', errorCode: 'mocked' };

      server.use(
        http.put(`${process.env.NEXT_PUBLIC_API_URL}${SETTINGS_ROUTES.THEMES}`, () =>
          HttpResponse.json(mockErrorRes, { status: 400 })
        )
      );

      try {
        await settingService.updateThemesSetting(MOCK_THEMES_SETTINGS_TEST_DATA);
      } catch (error: any) {
        expect(error).toBeInstanceOf(ApiError);
        expect(error.message).toBe(mockErrorRes.message);
        expect(error.errorCode).toBe(mockErrorRes.errorCode);
      }

      expect(errorService.handleError).toHaveBeenCalledOnce();
    });
  });
});
