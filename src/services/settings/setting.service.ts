import axiosInstance from '@/providers/axios/axios';
import { SETTINGS_ROUTES } from '@/router/api-routes';
import type {
  PersonalFormData,
  SafetyFormData,
  Setting,
  SettingCategory,
  SettingOption,
  SettingService,
  ThemesFormData,
} from '@/services/settings/setting.type';
import type { ApiResponse } from '@/types/api-responses.type';

/**
 * Setting service for handling setting-related operations
 */
class SettingServiceImpl implements SettingService {
  /**
   * Retrieves settings for a specific category
   *
   * @param category - The setting category to retrieve
   * @returns Promise resolving to an array of settings
   */
  getSettings = (category: SettingCategory) =>
    axiosInstance
      .get<ApiResponse<Setting[]>>(SETTINGS_ROUTES.CATEGORY(category))
      .then((r) => r.data);

  /**
   * Retrieves available options for settings in a specific category
   *
   * @param category - The setting category to get options for
   * @returns Promise resolving to setting options
   */
  getSettingOptions = (category: SettingCategory) =>
    axiosInstance
      .get<ApiResponse<SettingOption>>(SETTINGS_ROUTES.CATEGORY_OPTIONS(category))
      .then((r) => r.data);

  /**
   * Updates personal settings for the current user
   *
   * @param formData - Personal setting form data
   * @returns Promise resolving to the update result
   */
  updatePersonalSetting = (formData: PersonalFormData) =>
    axiosInstance.put<ApiResponse>(SETTINGS_ROUTES.PERSONAL, formData).then((r) => r.data);

  /**
   * Updates safety and security settings
   *
   * @param formData - Safety setting form data
   * @returns Promise resolving to the update result
   */
  updateSafetySetting = (formData: SafetyFormData) =>
    axiosInstance.put<ApiResponse>(SETTINGS_ROUTES.SAFETY, formData).then((r) => r.data);

  /**
   * Updates theme and UI settings
   *
   * @param formData - Theme setting form data
   * @returns Promise resolving to the update result
   */
  updateThemesSetting = (formData: ThemesFormData) =>
    axiosInstance.put<ApiResponse>(SETTINGS_ROUTES.THEMES, formData).then((r) => r.data);
}

export const settingService = new SettingServiceImpl();
