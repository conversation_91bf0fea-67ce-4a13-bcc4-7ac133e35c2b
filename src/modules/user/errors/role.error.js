import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const ROLE_ERROR_DEF = {
  updateNotAllowedForHierarchy: ['80005', 'error.roles.sentence.updateNotAllowedForHierarchy', 403],
  roleNameExists: ['80003', 'error.validation.sentence.exists', 409],
  invalidHierarchy: ['80004', 'error.roles.sentence.invalidHierarchy', 400],
  parentHasNoPoliciesAccess: ['80005', 'error.roles.sentence.parentHasNoPoliciesAccess', 400],
  exceedsParentPolicies: ['80006', 'error.roles.sentence.exceedsParentPolicies', 400],
  unsupportedModulePolicies: ['80007', 'error.roles.sentence.unsupportedModulePolicies', 400],
  noEntityAccess: ['80008', 'error.roles.sentence.noEntityAccess', 403],
  noRolePolicyAccess: ['80009', 'error.roles.sentence.noRolePolicyAccess', 403],
  noHierarchyModuleAccess: ['80010', 'error.roles.sentence.noHierarchyModuleAccess', 403],
  moduleAccessDenied: ['80011', 'error.roles.sentence.moduleAccessDenied', 403],
  actionNotAllowed: ['80012', 'error.roles.sentence.actionNotAllowed', 403],
};

export const roleError = createModuleErrors(MODULE_NAMES.ROLE, ROLE_ERROR_DEF);
