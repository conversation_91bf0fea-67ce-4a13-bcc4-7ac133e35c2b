import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { DepartmentConstant } from '#src/modules/user/constants/index.js';
import { DepartmentRepository } from '#src/modules/user/repository/index.js';
import { ModuleRepository } from '#src/modules/core/repository/index.js';
import { fetchFromCache } from '#src/utils/cache.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

const { HIERARCHY } = DepartmentConstant;
const {
  COMMON_STATUSES: { DELETED },
} = CoreConstant;

/**
 * Retrieves all departments for a given entity.
 *
 * @async
 * @param {Object} request - The request object containing necessary information.
 * @param {Object} request.entity - The entity object.
 * @param {string} request.entity.id - The ID of the entity.
 * @param {Object} request.query - The query parameters for filtering departments.
 * @param {Object} request.server - The server object, likely containing database connection information.
 * @param {boolean} isTemplate - Indicates whether the department is a template.
 * @returns {Promise<Array>} A promise that resolves to an array of department objects.
 */
export const index = async (request, isTemplate) => {
  const {
    entity: { id: entityId },
    query,
    server,
  } = request;

  const queryWithEntity = {
    ...query,
    filter_entityId_eq: entityId,
    filter_template_eq: isTemplate,
  };

  return await DepartmentRepository.findAll(server, queryWithEntity);
};

/**
 * Retrieves a department by its ID.
 *
 * @async
 * @param {Object} request - The request object containing necessary information.
 * @param {Object} request.params - The parameters object from the request.
 * @param {string} request.params.id - The ID of the department to retrieve.
 * @param {Object} request.server - The server object, likely containing database connection information.
 * @throws {CoreError} Throws a error if the department is not found.
 * @returns {Promise<Object>} A promise that resolves to the department object if found.
 */
export const view = async (request) => {
  const {
    params: { id },
    server,
  } = request;
  const department = await DepartmentRepository.findById(server, id);
  if (!department) {
    throw CoreError.dataNotFound({ data: 'common.label.department', attribute: 'ID', value: id });
  }

  const formattedModules = {};

  department.departmentModules.forEach((depModule) => {
    const { module, policySetting } = depModule;
    const hierarchy = module.hierarchy || 'root';

    if (!formattedModules[hierarchy]) {
      formattedModules[hierarchy] = [];
    }

    const formattedModule = {
      id: module.id,
      name: module.name,
      hierarchy: module.hierarchy,
      translationKey: module.translationKey,
      level: module.level,
      parentId: module.parentId,
      policySetting: Object.keys(policySetting.toJSON()).filter(
        (key) => policySetting[key] === true && key.startsWith('can'),
      ),
    };

    formattedModules[hierarchy].push(formattedModule);
  });

  return {
    ...department.toJSON(),
    modules: formattedModules,
  };
};

/**
 * Validates and filters module policies based on hierarchy and supported policies.
 * @param {Object} module - The module to validate against.
 * @param {string} departmentHierarchy - The hierarchy of the department.
 * @param {Array} policySetting - The policy settings to validate.
 * @returns {Array|null} Validated policy settings or null if invalid.
 */
export const validateAndFilterPolicies = (module, departmentHierarchy, policySetting) => {
  const isHierarchyValid =
    departmentHierarchy === HIERARCHY.ROOT ||
    (departmentHierarchy === HIERARCHY.ORGANISATION && module.hierarchy !== HIERARCHY.ROOT) ||
    (departmentHierarchy === HIERARCHY.MERCHANT && module.hierarchy === HIERARCHY.MERCHANT);

  if (!isHierarchyValid) {
    return null;
  }

  const modulePolicies = module.policySetting ? module.policySetting.toJSON() : {};
  const supportedPolicies = Object.keys(modulePolicies || {}).filter(
    (key) => modulePolicies[key] === true && key.startsWith('can'),
  );
  return policySetting.filter((policy) => supportedPolicies.includes(policy));
};

/**
 * Creates or updates a department module and its policy settings.
 * @param {Object} server - The server object.
 * @param {string} departmentId - The ID of the department.
 * @param {string} moduleId - The ID of the module.
 * @param {Array} validPolicySetting - The validated policy settings.
 * @param {Object} options - Additional options including transaction and authInfoId.
 * @returns {Promise<void>}
 */
export const upsertDepartmentModule = async (
  server,
  departmentId,
  moduleId,
  validPolicySetting,
  options,
) => {
  const { transaction, authInfoId } = options;
  let departmentModule = await DepartmentRepository.findDepartmentModule(
    server,
    departmentId,
    moduleId,
    { transaction },
  );

  if (!departmentModule) {
    departmentModule = await DepartmentRepository.createDepartmentModule(
      server,
      { departmentId, moduleId },
      { transaction, authInfoId },
    );
  }

  return await DepartmentRepository.upsertPolicySetting(
    server,
    {
      parentId: departmentModule.id,
      canView: validPolicySetting.includes('canView'),
      canCreate: validPolicySetting.includes('canCreate'),
      canEdit: validPolicySetting.includes('canEdit'),
      canImport: validPolicySetting.includes('canImport'),
      canExport: validPolicySetting.includes('canExport'),
      canManage: validPolicySetting.includes('canManage'),
      canMasking: validPolicySetting.includes('canMasking'),
      canOverwrite: validPolicySetting.includes('canOverwrite'),
      canVerify: validPolicySetting.includes('canVerify'),
    },
    { transaction, authInfoId },
  );
};

/**
 * Creates a new department with associated modules and policy settings.
 *
 * @async
 * @param {Object} request - The request object containing department creation details.
 * @param {Object} request.body - The body of the request containing department information.
 * @param {string} request.body.name - The name of the department.
 * @param {string} request.body.description - The description of the department.
 * @param {string} request.body.hierarchy - The hierarchy level of the department.
 * @param {string} request.body.status - The status of the department.
 * @param {Array} request.body.modules - An array of module objects to be associated with the department.
 * @param {Object} request.server - The server object, likely containing database connection information.
 * @param {Object} request.entity - The entity object associated with the request.
 * @param {string} request.entity.id - The ID of the entity.
 * @param {Object} request.authInfo - The authentication information.
 * @param {string} request.authInfo.id - The ID of the authenticated user.
 * @param {boolean} isTemplate - Indicates whether the department is a template.
 * @returns {Promise<Object>} A promise that resolves to the created department object.
 */
export const create = async (request, isTemplate) => {
  const {
    body: { name, description, hierarchy, status, modules },
    server,
    entity: { id: entityId },
    authInfo: { id: authInfoId },
  } = request;

  const allModules = await ModuleRepository.findAll(server);

  const department = await withTransaction(server, {}, async (transaction) => {
    const department = await DepartmentRepository.createDepartment(
      server,
      { name, description, hierarchy, status, template: isTemplate, entityId },
      { transaction, authInfoId },
    );

    await Promise.all(
      modules.map(async (moduleData) => {
        const module = allModules.find((m) => m.id === moduleData.module_id);
        if (!module) {
          return;
        }

        const validPolicySetting = validateAndFilterPolicies(
          module,
          hierarchy,
          moduleData.policySetting,
        );
        if (validPolicySetting && validPolicySetting.length > 0) {
          await upsertDepartmentModule(server, department.id, module.id, validPolicySetting, {
            transaction,
            authInfoId,
          });
        }
      }),
    );

    return department;
  });

  return await view({ server, params: { id: department.id } });
};

/**
 * Updates the basic information of a department.
 *
 * @async
 * @param {Object} request - The request object containing the update information.
 * @param {Object} request.body - The body of the request containing the updated department information.
 * @param {Object} request.authInfo - The authentication information of the user making the request.
 * @param {string} request.authInfo.id - The ID of the authenticated user.
 * @returns {Promise<Object>} A promise that resolves to the updated department object.
 */
export const updateBasicInformation = async (request) => {
  const {
    params: { id },
    body,
    authInfo: { id: authInfoId },
    server,
  } = request;
  const department = await DepartmentRepository.findById(server, id);
  if (!department) {
    throw CoreError.dataNotFound({ data: 'common.label.department', attribute: 'ID', value: id });
  }
  const res = await DepartmentRepository.update(department, body, { authInfoId });
  if (!res.isDirty) {
    throw CoreError.unprocessable();
  }
};

/**
 * Updates the module policies for a given department.
 *
 * @async
 * @param {Object} request - The request object containing necessary information.
 * @param {Object} request.params - The parameters object from the request.
 * @param {string} request.params.id - The ID of the department to update.
 * @param {Array} request.body - An array of module policy objects to update.
 * @param {Object} request.authInfo - The authentication information.
 * @param {string} request.authInfo.id - The ID of the authenticated user.
 * @param {Object} request.server - The server object, likely containing database connection information.
 * @returns {Promise<void>} A promise that resolves when all module policies have been updated.
 */
export const updateModulePolicy = async (request) => {
  const {
    params: { id: departmentId },
    body: newModulePolicies,
    authInfo: { id: authInfoId },
    server,
  } = request;

  return await withTransaction(server, {}, async (transaction) => {
    const department = await DepartmentRepository.findById(server, departmentId, { transaction });
    if (!department) {
      throw CoreError.dataNotFound({
        data: 'common.label.department',
        attribute: 'ID',
        value: departmentId,
      });
    }

    const allModules = await ModuleRepository.findAll(server);
    const existingDepartmentModules = await DepartmentRepository.findAllDepartmentModules(
      server,
      departmentId,
      { transaction },
    );

    let isDirty = false;

    const existingModuleIds = new Set(existingDepartmentModules.map((dm) => dm.moduleId));
    await Promise.all(
      newModulePolicies.map(async (modulePolicy) => {
        const { module_id, policySetting } = modulePolicy;
        const module = allModules.find((m) => m.id === module_id);
        if (!module) {
          return;
        }

        const validPolicySetting = validateAndFilterPolicies(
          module,
          department.hierarchy,
          policySetting,
        );
        if (validPolicySetting && validPolicySetting.length > 0) {
          const upsertDepartmentModulesRes = await upsertDepartmentModule(
            server,
            departmentId,
            module_id,
            validPolicySetting,
            {
              transaction,
              authInfoId,
            },
          );
          isDirty = upsertDepartmentModulesRes?.isDirty ?? true;

          existingModuleIds.delete(module_id);
        } else {
          const departmentModule = existingDepartmentModules.find(
            (dm) => dm.moduleId === module_id,
          );
          if (departmentModule) {
            await DepartmentRepository.removeDepartmentModule(server, departmentModule.id, {
              transaction,
              authInfoId,
            });
          }
        }
      }),
    );

    if (!isDirty && existingModuleIds.size === 0) {
      throw CoreError.unprocessable();
    }

    // Remove remaining department modules
    await Promise.all(
      Array.from(existingModuleIds).map(async (moduleId) => {
        const departmentModule = existingDepartmentModules.find((dm) => dm.moduleId === moduleId);
        if (departmentModule) {
          await DepartmentRepository.removeDepartmentModule(server, departmentModule.id, {
            transaction,
            authInfoId,
          });
        }
      }),
    );
  });
};

/**
 * Updates the status of a department.
 *
 * @async
 * @param {Object} request - The request object containing the necessary information.
 * @param {Object} request.body - The body of the request.
 * @param {string} request.body.status - The new status to be set for the department.
 * @param {Object} request.authInfo - The authInfo object from the request.
 * @param {string} request.authInfo.id - The ID of the authInfo making the request.
 * @returns {Promise<Object>} A promise that resolves to the updated department object.
 */
export const updateStatus = async (request) => {
  const {
    params: { id },
    body: { status },
    authInfo: { id: authInfoId },
    server,
  } = request;
  const department = await DepartmentRepository.findById(server, id);
  if (!department) {
    throw CoreError.dataNotFound({ data: 'common.label.department', attribute: 'ID', value: id });
  }
  const res = await DepartmentRepository.update(department, { status }, { authInfoId });
  if (!res.isDirty) {
    throw CoreError.unprocessable();
  }
};

/**
 * Removes a department based on the given request.
 *
 * @async
 * @param {Object} request - The request object containing necessary information for department removal.
 * @param {Object} request.authInfo - The authInfo object from the request.
 * @param {string} request.authInfo.id - The ID of the authInfo initiating the removal.
 * @returns {Promise<Object>} A promise that resolves to the result of the department removal operation.
 */
export const remove = async (request) => {
  const {
    params: { id },
    authInfo: { id: authInfoId },
    server,
  } = request;
  const department = await DepartmentRepository.findById(server, id);
  if (!department) {
    throw CoreError.dataNotFound({ data: 'common.label.department', attribute: 'ID', value: id });
  }

  return await withTransaction(server, {}, async (transaction) => {
    await DepartmentRepository.update(department, { status: DELETED }, { authInfoId, transaction });
    return await DepartmentRepository.remove(department, { authInfoId, transaction });
  });
};

/**
 * Formats a module object by extracting and restructuring specific properties.
 *
 * @param {Object} module - The module object to be formatted.
 * @param {string} module.id - The unique identifier of the module.
 * @param {string} module.name - The name of the module.
 * @param {string} module.hierarchy - The hierarchy level of the module.
 * @param {number} module.navigationPosition - The navigation position of the module.
 * @param {string|null} [module.parentId] - The ID of the parent module, if any.
 * @param {string|null} [module.navigationUrl] - The navigation URL of the module, if any.
 * @param {Object|null} [module.policySetting] - The policy settings of the module, if any.
 * @param {boolean} [module.policySetting.canView] - Whether viewing is allowed.
 * @param {boolean} [module.policySetting.canCreate] - Whether creation is allowed.
 * @param {boolean} [module.policySetting.canEdit] - Whether editing is allowed.
 * @param {boolean} [module.policySetting.canImport] - Whether importing is allowed.
 * @param {boolean} [module.policySetting.canExport] - Whether exporting is allowed.
 * @param {boolean} [module.policySetting.canManage] - Whether managing is allowed.
 * @param {boolean} [module.policySetting.canMasking] - Whether masking is allowed.
 * @param {boolean} [module.policySetting.canOverwrite] - Whether overwriting is allowed.
 * @param {boolean} [module.policySetting.canVerify] - Whether flagging is allowed.
 * @returns {Object} A new object containing the formatted module data.
 */
export const formatModule = (module) => {
  const formattedModule = {
    id: module.id,
    name: module.name,
    hierarchy: module.hierarchy,
    navigationPosition: module.navigationPosition,
    navigationType: module.navigationType,
    translationKey: module.translationKey,
    level: module.level,
  };

  if (module.policySetting) {
    const policySetting = module.policySetting.toJSON();
    formattedModule.policySetting = Object.keys(policySetting).filter(
      (key) =>
        typeof policySetting[key] === 'boolean' &&
        policySetting[key] === true &&
        key !== 'id' &&
        !key.startsWith('created') &&
        !key.startsWith('updated'),
    );
  }
  if (module.parentId !== null) {
    formattedModule.parentId = module.parentId;
  }

  return formattedModule;
};

/**
 * Filters and structures modules based on allowed hierarchies.
 *
 * @param {Array} modules - An array of module objects to be filtered and structured.
 * @param {Array<string>} allowedHierarchies - An array of hierarchy levels that are allowed.
 * @returns {Object} An object containing structured modules, organized by hierarchy (ROOT, ORGANISATION, MERCHANT).
 *                   Each hierarchy contains an array of modules that belong to that level.
 */
export const filterAndStructureModules = (modules, allowedHierarchies) => {
  const structuredModules = {
    [HIERARCHY.ROOT]: [],
    [HIERARCHY.ORGANISATION]: [],
    [HIERARCHY.MERCHANT]: [],
  };

  const processModule = (module) => {
    // Only process modules in allowed hierarchies
    if (!allowedHierarchies.includes(module.hierarchy)) {
      return null;
    }

    // Recursively process children
    const formattedModule = formatModule(module);
    if (Array.isArray(module.children) && module.children.length > 0) {
      formattedModule.children = module.children.map(processModule).filter(Boolean); // Remove disallowed/nulls
    }

    // Only top-level modules are added to structuredModules
    if (module.parentId === null) {
      structuredModules[module.hierarchy].push(formattedModule);
    }

    return formattedModule;
  };

  modules.forEach(processModule);

  return structuredModules;
};

/**
 * Retrieves module policy options based on the given request.
 * This function is an asynchronous wrapper around getModulePolicyOptions.
 *
 * @param {Object} request - The request object containing entity information.
 * @param {Object} request.server - The server object, likely containing Redis instance.
 * @param {Object} request.entity - The entity object with hierarchy information.
 * @param {string} request.entity.id - The unique identifier of the entity.
 * @param {string} request.entity.hierarchy - The hierarchy of the entity (ROOT, ORGANISATION, or MERCHANT).
 * @returns {Promise<Object>} A promise that resolves to an object containing structured modules,
 *                            organized by hierarchy (ROOT, ORGANISATION, MERCHANT).
 */
export const getModulePolicyOptions = async (request) => {
  const {
    server,
    entity: { id: entityId, hierarchy },
  } = request;

  let allowedHierarchies;
  switch (hierarchy) {
    case HIERARCHY.ROOT:
      allowedHierarchies = [HIERARCHY.ROOT, HIERARCHY.ORGANISATION, HIERARCHY.MERCHANT];
      break;
    case HIERARCHY.ORGANISATION:
      allowedHierarchies = [HIERARCHY.ORGANISATION, HIERARCHY.MERCHANT];
      break;
    case HIERARCHY.MERCHANT:
      allowedHierarchies = [HIERARCHY.MERCHANT];
      break;
    default:
      throw new Error(`Invalid hierarchy: ${hierarchy}`);
  }

  const cacheKey = `module_policy:${entityId}:${hierarchy}`;

  return fetchFromCache(server.redis, cacheKey, async () => {
    const modules = await ModuleRepository.findModulePolicies(server, allowedHierarchies);
    return filterAndStructureModules(modules, allowedHierarchies);
  });
};

/**
 * Retrieves module policy options based on the given request.
 *
 * @async
 * @param {Object} request - The request object containing necessary information for fetching policy options.
 * @returns {Promise<Object>} A promise that resolves to an object containing the module policy options.
 */
export const options = async (request) => await getModulePolicyOptions(request);
