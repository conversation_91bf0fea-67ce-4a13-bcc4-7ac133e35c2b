import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const AUDIT_TRAIL_ERROR_DEF = {
  invalidDateRange: ['40001', 'error.auditTrails.sentence.invalidDateRange', 400],
  entityAccessIdRequired: ['40002', 'error.validation.sentence.required', 400],
};

export const auditTrailError = createModuleErrors(MODULE_NAMES.AUDIT_TRAIL, AUDIT_TRAIL_ERROR_DEF);
