import { DataTypes, Model, Sequelize } from 'sequelize';
import { createHash } from 'crypto';

import { COMMON_STATUSES, REMARK_STATUSES } from '#src/modules/core/constants/core.constant.js';
import { auditableMixin, versionedMixin } from '#src/mixins/index.js';
import { DeveloperHubError } from '#src/modules/setting/errors/index.js';
import { DeveloperHubRepository } from '#src/modules/setting/repository/index.js';

const hashApiKey = (key) => createHash('sha256').update(key).digest('hex');

const checkApiKeyExists = async (instance) => {
  const server = {
    psql: {
      DeveloperHub: instance.constructor,
    },
  };
  const existingApiKey = await DeveloperHubRepository.findByApiKey(server, instance.apiKey);
  if (existingApiKey) {
    throw DeveloperHubError.apiKeyExists({
      attribute: 'common.label.apiKey',
      value: '',
    });
  }
};

export default function (fastify, instance) {
  class DeveloperHub extends Model {
    static associate(models) {
      DeveloperHub.hasMany(models.ApiRight, {
        foreignKey: 'parentId',
        as: 'apiRights',
      });

      DeveloperHub.hasMany(models.IpAccessControl, {
        foreignKey: 'parentId',
        as: 'ipAccessControls',
        scope: {
          status: COMMON_STATUSES.ACTIVE,
        },
        include: [
          {
            model: models.Remark,
            as: 'activeRemark',
            scope: {
              remarkable_type: 'ip_access_control',
              status: REMARK_STATUSES.ACTIVE,
            },
          },
        ],
      });
    }
  }

  DeveloperHub.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      application: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      apiKey: {
        type: DataTypes.STRING(64),
        allowNull: false,
        async validateApiKey(plainApiKey) {
          return this.apiKey === hashApiKey(plainApiKey);
        },
      },
      expiryDate: {
        type: DataTypes.DATE(),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(Object.values(COMMON_STATUSES)),
        allowNull: false,
        defaultValue: COMMON_STATUSES.ACTIVE,
      },
      version: {
        type: DataTypes.BIGINT,
        allowNull: false,
        defaultValue: 1,
        validate: {
          min: 1,
        },
      },
      ipAddresses: {
        type: DataTypes.VIRTUAL,
        get() {
          if (this.ipAccessControls) {
            return this.ipAccessControls.map((control) => control.ipAddress);
          }
          return null;
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'DeveloperHub',
      tableName: 'developer_hubs',
      underscored: true,
      timestamps: true,
      paranoid: true,
      sequelize: fastify.psql.connection,
      hooks: {
        beforeCreate: async (instance, options) => {
          instance.apiKey = hashApiKey(instance.apiKey);

          await checkApiKeyExists(instance);
        },
      },
    },
  );

  auditableMixin.applyAuditFields(DeveloperHub);
  versionedMixin.applyVersioning(DeveloperHub);

  return DeveloperHub;
}
