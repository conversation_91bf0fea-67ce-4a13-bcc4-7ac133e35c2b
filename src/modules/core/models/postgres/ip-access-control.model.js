import { DataTypes, Model, Sequelize } from 'sequelize';
import { auditableMixin } from '#src/mixins/index.js';

import { AccessControlConstant } from '#src/modules/setting/constants/index.js';
import { AccessControlError } from '#src/modules/setting/errors/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { IpAccessControlRepository } from '#src/modules/setting/repository/index.js';

const { REMARK_STATUSES } = CoreConstant;
const { RULE_TYPE, STATUSES } = AccessControlConstant;
const { BLOCKLIST } = RULE_TYPE;

export default function (fastify, instance) {
  class IpAccessControl extends Model {
    static associate(models) {
      IpAccessControl.belongsTo(models.Remark, {
        foreignKey: 'id',
        targetKey: 'remarkableId', // Linking IpAccessControl.id to Remark.remarkableId for polymorphic relation
        constraints: false,
        as: 'activeRemark',
        scope: {
          remarkable_type: 'ip_access_control',
          status: REMARK_STATUSES.ACTIVE,
        },
      });
    }
  }

  IpAccessControl.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      parentId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      ruleType: {
        type: DataTypes.ENUM(Object.values(RULE_TYPE)),
        allowNull: false,
        defaultValue: BLOCKLIST,
      },
      ipAddress: {
        type: DataTypes.INET,
        allowNull: false,
      },
      validityDate: {
        type: DataTypes.DATE(),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(Object.values(STATUSES)),
        allowNull: false,
        defaultValue: STATUSES.ACTIVE,
      },
      remark: {
        type: DataTypes.VIRTUAL,
        get() {
          return this.activeRemark?.content;
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'IpAccessControl',
      tableName: 'ip_access_controls',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['parent_id', 'ip_address'],
          name: 'idx_ipac_unique_parent_ip',
        },
        {
          fields: ['parent_id', 'rule_type', 'status', 'validity_date'],
          name: 'idx_ipac_parent_rule_status_validity',
          comment:
            'Improves query performance when filtering or joining by parent_id, rule_type, status, and validity_date',
        },
        {
          fields: ['parent_id'],
          name: 'idx_ipac_parent',
          comment: 'Improves query performance when filtering or joining by parent_id',
        },
        {
          fields: ['rule_type'],
          name: 'idx_ipac_rule_type',
          comment: 'Enhances performance for queries that filter by rule_type',
        },
        {
          fields: ['status'],
          name: 'idx_ipac_status',
          comment: 'Improves performance for queries that filter by status',
        },
        {
          fields: ['validity_date'],
          name: 'idx_ipac_validity_date',
          comment: 'Speeds queries on expiration dates',
        },
      ],
      hooks: {
        afterFind: (records) => {
          if (!Array.isArray(records)) {
            records = [records];
          }
          records.forEach((record) => {
            if (record?.validityDate && new Date(record.validityDate) < new Date()) {
              record.status = STATUSES.EXPIRED;
            }
          });
        },
        beforeCreate: async (instance, options) => {
          const { constructor, ipAddress, parentId } = instance;
          const server = {
            psql: {
              IpAccessControl: constructor,
            },
          };

          const existingRecord = await IpAccessControlRepository.checkOverlap(
            server,
            parentId,
            ipAddress,
          );
          if (existingRecord) {
            throw AccessControlError.ipExists({
              attribute: 'common.label.ipAddress',
              value: ipAddress,
            });
          }
        },
        beforeUpdate: async (instance, options) => {
          const { constructor, id, ipAddress, parentId } = instance;
          const server = {
            psql: {
              IpAccessControl: constructor,
            },
          };

          const changedFields = instance.changed();
          if (
            changedFields &&
            changedFields.length > 0 &&
            (changedFields.includes('ipAddress') ||
              (changedFields.includes('status') && instance.status === STATUSES.ACTIVE))
          ) {
            const existingRecord = await IpAccessControlRepository.checkOverlap(
              server,
              parentId,
              ipAddress,
              instance.id,
            );
            if (existingRecord && existingRecord.id !== id) {
              throw AccessControlError.ipExists({
                attribute: 'common.label.ipAddress',
                value: ipAddress,
              });
            }
          }
        },
        afterDestroy: async (instance, options) => {
          const { transaction } = options;
          const { id } = instance;
          const Remark = instance.sequelize.models.Remark;
          await Remark.destroy({
            where: {
              remarkableId: id,
            },
            transaction,
          });
        },
      },
    },
  );

  auditableMixin.applyAuditFields(IpAccessControl);

  return IpAccessControl;
}
