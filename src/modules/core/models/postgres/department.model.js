import { DataTypes, Model, Sequelize } from 'sequelize';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DepartmentError } from '#src/modules/user/errors/index.js';
import { auditableMixin } from '#src/mixins/index.js';

const { ACCESS_LEVELS: HIERARCHY, COMMON_STATUSES } = CoreConstant;

export default function (fastify, instance) {
  class Department extends Model {
    static associate(models) {
      this.hasMany(models.DepartmentModule, {
        foreignKey: 'departmentId',
        as: 'departmentModules',
      });
    }
  }

  Department.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      entityId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING(50),
        allowNull: false,
        validate: {
          len: [1, 50],
          notEmpty: true,
        },
      },
      hierarchy: {
        type: DataTypes.ENUM(Object.values(HIERARCHY)),
        allowNull: false,
      },
      template: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      description: {
        type: DataTypes.STRING(100),
        allowNull: true,
        validate: {
          len: [1, 100],
          notEmpty: false,
        },
      },
      status: {
        type: DataTypes.ENUM(Object.values(COMMON_STATUSES)),
        allowNull: false,
        defaultValue: COMMON_STATUSES.ACTIVE,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'Department',
      tableName: 'departments',
      underscored: true,
      timestamps: true,
      paranoid: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          fields: ['entity_id'],
          name: 'idx_departments_entity',
        },
        {
          fields: ['entity_id', 'hierarchy'],
          name: 'idx_departments_entity_hierarchy',
        },
        {
          fields: ['status'],
          name: 'idx_departments_status',
        },
      ],
      hooks: {
        beforeCreate: async (department, options) => {
          await checkDuplicateDepartment(department, options);
        },
        beforeUpdate: async (department, options) => {
          await checkDuplicateDepartment(department, options);
        },
      },
    },
  );

  /**
   * Checks for duplicate departments within the same entity.
   *
   * @async
   * @param {Object} department - The department object to check for duplicates.
   * @param {string} department.entityId - The ID of the entity the department belongs to.
   * @param {string} department.name - The name of the department.
   * @param {string} department.id - The ID of the department (for update operations).
   * @param {Object} options - Additional options for the database operation.
   * @param {Object} options.transaction - The database transaction to use for this operation.
   * @throws {DepartmentError} Throws a DepartmentError if a duplicate department is found.
   * @returns {Promise<void>} Resolves if no duplicate is found, otherwise throws an error.
   */
  async function checkDuplicateDepartment(department, options) {
    const { entityId, name } = department;
    const existingDepartment = await Department.findOne({
      where: {
        entityId,
        name,
        id: { [Sequelize.Op.ne]: department.id },
      },
      transaction: options.transaction,
    });

    if (existingDepartment) {
      throw DepartmentError.departmentExists({
        attribute: 'common.label.department',
        value: name,
      });
    }
  }

  auditableMixin.applyAuditFields(Department);

  return Department;
}
