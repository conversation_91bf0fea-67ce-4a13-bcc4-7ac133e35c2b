import {
  ModuleRepository,
  PolicySettingRepository,
  RoleModuleRepository,
  RoleRepository,
} from '#src/modules/user/repository/index.js';

import { CoreError } from '#src/modules/core/errors/index.js';
import { RoleError } from '#src/modules/user/errors/index.js';

/**
 * Validates role name uniqueness.
 * @param {Object} server - The server object.
 * @param {string} entityId - The entity ID.
 * @param {string} name - The role name.
 * @param {string|null} excludeId - The role ID to exclude.
 * @throws {Error} If name is not unique.
 */
export const validateRoleName = async (server, entityId, name, excludeId = null) => {
  const existingRole = await RoleRepository.findByEntityIdAndName(
    server,
    entityId,
    name,
    excludeId,
  );

  if (existingRole) {
    throw RoleError.roleNameExists({ attribute: 'common.label.role', value: name });
  }
};

/**
 * Validates and filters module policies.
 * @param {Object} server - The server object.
 * @param {Array} modules - Array of modules with policies.
 * @param {string} hierarchy - The user hierarchy.
 * @param {string|null} parentId - The parent role ID.
 * @returns {Promise<Array>} Filtered module policies.
 * @throws {Error} If validation fails.
 */
export const validateAndFilterModulePolicies = async (
  server,
  modules,
  hierarchy,
  parentId = null,
) => {
  let parentPolicies = {};
  if (parentId) {
    const parentModulePolicies = await RoleModuleRepository.findAllByRoleIdWithPolicySettings(
      server,
      parentId,
    );

    parentPolicies = parentModulePolicies.reduce((acc, modulePolicy) => {
      acc[modulePolicy.moduleId] = modulePolicy.policySetting.toJSON();
      return acc;
    }, {});
  }

  const validatedModules = await Promise.all(
    modules.map(async (module) => {
      const moduleId = typeof module === 'object' ? module.moduleId : module;
      let requestedPolicies = typeof module === 'object' ? module.policies : [];

      const moduleInfo = await ModuleRepository.findById(server, moduleId);

      if (!moduleInfo) {
        throw CoreError.dataNotFound({
          data: 'common.label.module',
          attribute: 'ID',
          value: moduleId,
        });
      }

      requestedPolicies = validatePoliciesForHierarchy(
        requestedPolicies,
        moduleInfo.hierarchy,
        hierarchy,
      );

      const supportedPolicies = await PolicySettingRepository.findByParentId(server, moduleId);

      const unsupportedPolicies = requestedPolicies.filter((policy) => !supportedPolicies[policy]);
      if (unsupportedPolicies.length > 0) {
        throw RoleError.unsupportedModulePolicies({
          module: moduleInfo.translation_key,
          policies: unsupportedPolicies,
        });
      }

      if (parentId) {
        if (!parentPolicies[moduleId]) {
          throw RoleError.parentHasNoPoliciesAccess({ module: moduleInfo.translation_key });
        }

        const exceedingParentPolicies = requestedPolicies.filter(
          (policy) => !parentPolicies[moduleId][policy],
        );
        if (exceedingParentPolicies.length > 0) {
          throw RoleError.exceedsParentPolicies();
        }
      }

      return { moduleId, policies: requestedPolicies };
    }),
  );

  return validatedModules;
};

/**
 * Validates policies for a given hierarchy.
 * @param {Array} policies - The policies to validate.
 * @param {string} moduleHierarchy - The module hierarchy.
 * @param {string} hierarchy - The user hierarchy.
 * @returns {Array} The validated policies.
 * @throws {Error} If the hierarchy is invalid for the module.
 */
export const validatePoliciesForHierarchy = (policies, moduleHierarchy, hierarchy) => {
  const allowedHierarchies = {
    root: ['root', 'organisation', 'merchant'],
    organisation: ['organisation', 'merchant'],
    merchant: ['merchant'],
  };

  if (!allowedHierarchies[hierarchy].includes(moduleHierarchy)) {
    throw RoleError.invalidHierarchy();
  }

  return policies;
};

/**
 * Checks if a role is within the user's hierarchy.
 * @param {string} userPath - The user's role path.
 * @param {string} rolePath - The role path to check.
 * @returns {boolean} True if the role is within the hierarchy.
 */
export const validateHierarchy = (userPath, rolePath) => {
  return rolePath.startsWith(userPath);
};
