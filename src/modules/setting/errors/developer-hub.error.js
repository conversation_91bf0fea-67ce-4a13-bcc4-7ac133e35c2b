import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const DEVELOPER_HUB_ERROR_DEF = {
  apiKeyExists: ['54409', 'error.validation.sentence.exists', 409],
  atLeastOneRequired: ['54400', 'error.validation.sentence.atLeastOneRequired', 400],
};

export const developerHubError = createModuleErrors(
  MODULE_NAMES.DEVELOPER_HUB,
  DEVELOPER_HUB_ERROR_DEF,
);
