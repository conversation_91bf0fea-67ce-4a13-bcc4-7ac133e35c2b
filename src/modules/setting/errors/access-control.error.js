// errors/accessControlErrors.js
import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const ACCESS_CONTROL_ERROR_DEF = {
  ipBlocked: ['53401', 'error.accessControls.sentence.ipBlocked', 403],
  ipExists: ['53409', 'error.validation.sentence.exists', 409],
};

export const accessControlError = createModuleErrors(
  MODULE_NAMES.ACCESS_CONTROL,
  ACCESS_CONTROL_ERROR_DEF,
);
