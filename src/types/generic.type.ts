export interface Pid {
  id: Uuid;
}

export interface Version {
  version: number;
}

/**
 * Represents the state of an alert message shown to the user.
 * Can be used for success, error, warning, or informational messages.
 */

// Explicit severity type for reusability
export type AlertSeverity = 'error' | 'success' | 'info' | 'warning';

export type AlertState = {
  severity: AlertSeverity;
  title?: string;
  message?: string;
} | null;
