import { RoleError } from '#src/modules/user/errors/index.js';
import { findByIdWithModulePolicies } from '#src/modules/user/repository/role.repository.js';

export const checkPolicies = async (request, fastify, routeConfig = {}) => {
  const requiredPolicy = routeConfig.policy;

  if (!requiredPolicy) {
    return;
  }

  if (!hasEntityAccess(request)) {
    throw RoleError.noEntityAccess();
  }

  const [module, action] = requiredPolicy.split('.');

  const hierarchy = request.entity?.hierarchy;
  const roleId = request.authInfo?.roleId;
  const userEntityId = request.userEntity?.id;

  const roleWithPolicies = await findByIdWithModulePolicies(fastify, userEntityId, roleId);

  if (!roleWithPolicies) {
    throw RoleError.noRolePolicyAccess();
  }

  const hierarchyModules = roleWithPolicies.modulePolicies[hierarchy];

  if (!hierarchyModules) {
    throw RoleError.noHierarchyModuleAccess({ hierarchy });
  }

  const modulePolicy = hierarchyModules.find(
    (policy) => policy.name.toLowerCase() === module.toLowerCase(),
  );
  if (!modulePolicy) {
    throw RoleError.moduleAccessDenied({ module });
  }

  if (!modulePolicy?.policySettings?.[action]) {
    throw RoleError.actionNotAllowed({ action, module });
  }
};

const hasEntityAccess = (request) => {
  const { userEntity, entity, parentEntity } = request;
  if (userEntity.hierarchy === 'root') {
    return true;
  }

  if (userEntity.hierarchy === 'organisation') {
    return (
      (entity.hierarchy === 'organisation' && entity.id === userEntity.id) ||
      (entity.hierarchy === 'merchant' && parentEntity.id === userEntity.id)
    );
  }

  if (userEntity.hierarchy === 'merchant') {
    return entity.id === userEntity.id;
  }

  return false;
};
