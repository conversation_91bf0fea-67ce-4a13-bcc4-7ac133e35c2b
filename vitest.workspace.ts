import { defineConfig } from 'vitest/config';

const config = defineConfig({
  test: {
    projects: [
      {
        extends: './config/vitest.config.mts',
        test: {
          name: 'Unit Tests',
          // Don't override include - let the base config handle it
        },
      },
      {
        extends: './config/vitest.storybook.config.mts',
        test: {
          name: 'Storybook Tests',
          // Don't override include - let the storybookTest plugin handle story discovery
        },
      },
    ],
  },
});

export default config.test!.projects;
